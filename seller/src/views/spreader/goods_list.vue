<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :title="L('推手商品管理')" />
      <div class="spreader_goods_lists">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="importGoods">
                <AliSvgIcon
                  iconName="icondaoru"
                  width="17px"
                  height="17px"
                  :fillColor="'#fa6f1e'"
                />
                <span style="margin-left: 4px">{{ L('从店铺导入商品') }}</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'goodsImage'">
              <div class="goods_info com_flex_row_flex_start">
                <div class="goods_img" style="border: none">
                  <Popover placement="rightTop">
                    <template #content>
                      <div style="width: 200px">
                        <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                      </div>
                    </template>
                    <div class="business_load_img">
                      <img :src="text" alt="" />
                    </div>
                  </Popover>
                </div>
                <div class="com_flex_column_space_between goods_detail">
                  <div
                    class="goods_name"
                    style="max-width: 380px; margin-top: 6px; white-space: initial"
                    :title="record.goodsName"
                  >
                    {{ record.goodsName }}
                  </div>
                  <span class="goods_brief" :title="record.labelName">
                    {{ record.labelName }}
                  </span>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <TableAction
                class="TableAction"
                :actions="[
                  {
                    label: L('设置佣金'),
                    onClick: setCommission.bind(null, record),
                  },
                  {
                    label: L('设置标签'),
                    onClick: setLabel.bind(null, record.spreaderGoodsId),
                  },
                  // 只有待发布、已失效、已结束的才可以删除
                  {
                    label: L('不参与推手'),
                    popConfirm: {
                      title: L('确定执行该操作'),
                      placement: 'left',
                      confirm: operateGoods.bind(null, record.spreaderGoodsId, 'cancle'),
                    },
                  },
                ]"
              />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <SldSelMoreLeftRightSpreaderGoods
      @confirm-event="seleGoods"
      @cancle-event="sldHandleCancle"
      :width="1000"
      :selectedRow="sele_more_goods.info"
      :selectedRowKey="sele_more_goods.ids"
      :title="L('选择商品(至少选择一个)')"
      :height="height - 400"
      :modalVisible="modalVisibleGoods"
      :extra="sele_more_goods"
    >
    </SldSelMoreLeftRightSpreaderGoods>
    <SldModal
      :width="500"
      :title="title"
      :visible="modalVisible"
      :height="600"
      :content="operateData"
      :showFoot="showFoot"
      :confirmBtnLoading="modalConfirmBtnLoading"
      @cancle-event="handleModalCancle"
      @confirm-event="sldConfirm"
      @callback-event="handleCommissionTypeChange"
    />
  </div>
</template>
<script>
  export default {
    name: 'SpreaderGoodsList',
  };
</script>
<script setup>
  import { getCurrentInstance, onMounted, unref, ref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import SldSelMoreLeftRightSpreaderGoods from '/@/components/SldSelMoreLeftRightSpreaderGoods/index.vue';
  import { Popover } from 'ant-design-vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { list_com_page_more } from '/@/utils/utils';
  import { getSettingListApi } from '/@/api/common/common';
  import {
    getSpreaderGoodsListApi,
    getSpreaderGoodsLabelListApi,
    getSpreaderGoodsAddApi,
    getSpreaderGoodsRemoveGoodsApi,
    getSpreaderGoodsBindLabelApi,
    getSpreaderGoodsSetCommissionApi,
  } from '/@/api/spreader/goods_list';
  import { validatorEmoji } from '/@/utils/validate';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const modalVisible = ref(false);

  const showFoot = ref(true);
  const modalConfirmBtnLoading = ref(false);
  const title = ref('');
  const type = ref('add');
  const operateData = ref([]);
  const operate_id = ref('');
  const currentProductPrice = ref(0); // 当前商品价格

  const enableFlag = ref(0); //推手开关
  const height = ref(document.body.clientHeight);
  const modalVisibleGoods = ref(false);
  const sele_more_goods = ref({
    info: [], //选择的商品数组
    ids: [], //选择的商品id数组
    min_num: 1, //最小数量，0为不限制
  });

  // 获取当前商品价格的函数
  const getCurrentProductPrice = () => {
    return currentProductPrice.value;
  };

  //设置商品佣金
  const addData = ref([
    {
      type: 'select',
      require: true,
      label: L('佣金类型'),
      name: 'commissionType',
      placeholder: L('请选择佣金类型'),
      selData: [
        { value: 'fixed', label: L('固定佣金') },
        { value: 'rate', label: L('佣金比例') },
      ],
      selKey: 'value',
      selName: 'label',
      diy: true,
      // initialValue 将在 setCommission 函数中动态设置
      callback: true, // 启用回调
      rules: [
        {
          required: true,
          message: `${L('请选择佣金类型')}`,
        },
      ],
    },
    {
      type: 'inputnum',
      require: true,
      label: L('佣金金额'),
      name: 'commission',
      placeholder: L('请输入固定佣金'),
      extra: L(
        "以'元'为单位，会员确认收货后，会把佣金按设定的比例返给会员的推手。最大可设置：¥{maxCommission}",
      ).replace('{maxCommission}', ''),
      value: '',
      min: 0.01,
      max: 999999,
      precision: 2,
      show: (values) => {
        if (!values || values.commissionType === undefined) return false;
        return values.commissionType === 'fixed';
      },
      rules: [
        {
          validator: async (rule, value, callback, values) => {
            if (values && values.commissionType === 'fixed') {
              if (!value || value <= 0) {
                return Promise.reject(new Error(L('请输入固定佣金')));
              }
              // 通过闭包获取商品价格进行验证
              const productPrice = getCurrentProductPrice();
              if (productPrice && value >= productPrice) {
                return Promise.reject(new Error(L('固定佣金不能大于等于商品价格')));
              }
              const maxCommission = productPrice ? productPrice - 0.01 : 999999;
              if (productPrice && value > maxCommission) {
                return Promise.reject(
                  new Error(`${L('固定佣金不能超过')} ¥${maxCommission.toFixed(2)}`),
                );
              }
            }
            return Promise.resolve();
          },
        },
      ],
    },
    {
      type: 'inputnum',
      require: true,
      label: L('佣金比例'),
      name: 'commissionRate',
      placeholder: L('请输入佣金比例(0.01%-100%)'),
      extra: L(
        "以'%'为单位，会员确认收货后，会把佣金按设定的比例返给会员的推手。可设置范围：0.01%-100%",
      ),
      value: '',
      min: 0.01,
      max: 100,
      precision: 2,
      callback: true, // 启用回调以便实时计算预计佣金
      show: (values) => {
        if (!values || values.commissionType === undefined) return false;
        return values.commissionType === 'rate';
      },
      rules: [
        {
          validator: async (rule, value, callback, values) => {
            if (values && values.commissionType === 'rate') {
              if (!value || value <= 0) {
                return Promise.reject(new Error(L('请输入佣金比例')));
              }
              if (value < 0.01) {
                return Promise.reject(new Error(L('佣金比例不能小于0.01%')));
              }
              if (value > 100) {
                return Promise.reject(new Error(L('佣金比例不能大于100%')));
              }
              // 通过闭包获取商品价格进行验证
              const productPrice = getCurrentProductPrice();
              if (productPrice) {
                const estimatedCommission = (productPrice * value) / 100;
                if (estimatedCommission >= productPrice) {
                  return Promise.reject(new Error(L('预计佣金不能大于等于商品价格')));
                }
              }
            }
            return Promise.resolve();
          },
        },
      ],
    },
    {
      type: 'show_content',
      name: 'estimatedCommission',
      label: L('预计佣金'),
      content: '¥0.00',
      show: (values) => {
        if (!values || values.commissionType === undefined) return false;
        return values.commissionType === 'rate';
      },
    },
  ]);

  const labelData = ref([
    {
      type: 'select',
      label: L('商品标签'), //下拉选择
      name: 'labelId',
      placeholder: L('请选择商品标签'),
      selData: [],
      selKey: 'labelId',
      selName: 'labelName',
      diy: true,
      rules: [
        {
          required: true,
          message: `${L('请选择商品标签')}`,
        },
      ],
    },
  ]);

  const [standardTable, { reload }] = useTable({
    api: (arg) => getSpreaderGoodsListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: L('商品信息'),
        dataIndex: 'goodsImage',
        width: 250,
      },
      {
        title: L('商品价格(¥)'),
        dataIndex: 'productPrice',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('商品佣金'),
        dataIndex: 'commission',
        width: 120,
        customRender: ({ text, record }) => {
          // 优先检查 commissionRate 字段（佣金比例）
          if (
            record.commissionRate !== undefined &&
            record.commissionRate !== null &&
            record.commissionRate !== '' &&
            record.commissionRate > 0
          ) {
            return `${record.commissionRate}%`;
          }
          // 然后检查 commission 字段（固定佣金）
          if (text !== undefined && text !== null && text !== '' && text > 0) {
            return `¥${text}`;
          }
          // 如果都没有，显示默认值
          return '--';
        },
      },
      {
        title: L('商品库存'),
        dataIndex: 'stock',
        width: 100,
      },
      {
        title: L('发布时间'),
        dataIndex: 'createTime',
        width: 100,
      },
    ],
    useSearchForm: true,
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    formConfig: {
      schemas: [
        {
          field: 'goodsName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: L('请输入商品名称'),
            size: 'default',
          },
          label: L('商品名称'),
          labelWidth: 70,
          rules: [
            {
              // @ts-ignore
              validator: async (rule, value) => {
                await validatorEmoji(rule, value);
              },
              trigger: 'change',
            },
          ],
        },
        {
          field: '[startTime,endTime]',
          component: 'RangePicker',
          colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
          componentProps: {
            format: 'YYYY-MM-DD',
            placeholder: [L('开始时间'), L('结束时间')],
          },
          label: L('发布时间'),
          labelWidth: 80,
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 220,
      title: L('操作'),
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  //获取所有标签
  const get_label_list = async () => {
    try {
      let res = await getSpreaderGoodsLabelListApi({ pageSize: list_com_page_more });
      if (res.state == 200) {
        labelData.value[0].selData = res.data;
      }
    } catch (error) {}
  };

  // 验证推手开关
  const checkSpreaderState = async () => {
    const res = await getSettingListApi({
      str: 'spreader_is_enable',
    });
    if (res.state == 200 && res.data) {
      enableFlag.value = res.data[0].value;
    }
  };

  const importGoods = () => {
    if (enableFlag.value == 0) {
      failTip(L('推手模块未开启'));
    } else {
      modalVisibleGoods.value = true;
    }
  };

  // 弹窗点击确定
  const seleGoods = async (selectedRowsP, selectedRowKeysP) => {
    let param_data = [];
    selectedRowsP.map((item) => {
      let goodsData = {
        goodsId: item.goodsId,
        goodsName: item.goodsName,
        labelId: item.labelId,
      };

      // 根据佣金类型添加对应的字段
      if (item.commissionType === 'fixed') {
        goodsData.commission = item.commission;
      } else {
        goodsData.commissionRate = item.commissionRate;
      }

      param_data.push(goodsData);
    });
    let res = await getSpreaderGoodsAddApi(param_data);
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
      modalVisibleGoods.value = false;
    } else {
      failTip(res.msg);
    }
  };

  // 关闭弹窗
  const sldHandleCancle = () => {
    modalVisibleGoods.value = false;
  };

  //设置佣金
  const setCommission = (val) => {
    operateData.value = [];
    // 使用扩展运算符进行深拷贝，保留函数
    operateData.value = addData.value.map((item) => ({ ...item }));

    // 设置当前商品价格供验证器使用
    currentProductPrice.value = val.productPrice || 0;

    // 设置固定佣金的最大值
    let commissionTarget = operateData.value.find((item) => item.name === 'commission');
    if (commissionTarget) {
      commissionTarget.max = val.productPrice * 1 - 0.01 || 0.01;

      // 更新佣金金额字段的提示信息，显示最大可设置金额
      const maxCommission = (val.productPrice - 0.01).toFixed(2);
      commissionTarget.extra =
        L("以'元'为单位，会员确认收货后，会把佣金按设定的比例返给会员的推手。最大可设置：¥") +
        maxCommission;
    }

    // 获取字段引用
    let commissionTypeTarget = operateData.value.find((item) => item.name === 'commissionType');
    let rateTarget = operateData.value.find((item) => item.name === 'commissionRate');
    let estimatedTarget = operateData.value.find((item) => item.name === 'estimatedCommission');

    // 根据接口返回数据判断佣金类型
    if (
      val.commissionRate !== undefined &&
      val.commissionRate !== null &&
      val.commissionRate !== '' &&
      val.commissionRate > 0
    ) {
      // 佣金比例模式
      if (commissionTypeTarget) commissionTypeTarget.initialValue = 'rate';
      if (rateTarget) rateTarget.initialValue = val.commissionRate;
      // 确保固定佣金字段为空
      if (commissionTarget) {
        commissionTarget.initialValue = undefined;
        commissionTarget.value = '';
      }
      // 计算并显示预计佣金
      if (estimatedTarget) {
        estimatedTarget.content = calculateEstimatedCommission(val.commissionRate);
      }

      console.log('回显为佣金比例模式:', {
        commissionType: 'rate',
        commissionRate: val.commissionRate,
        estimatedCommission: calculateEstimatedCommission(val.commissionRate),
      });
    } else {
      // 固定佣金模式
      if (commissionTypeTarget) commissionTypeTarget.initialValue = 'fixed';
      if (commissionTarget) commissionTarget.initialValue = val.commission || 0.01;
      // 确保佣金比例字段为空
      if (rateTarget) {
        rateTarget.initialValue = undefined;
        rateTarget.value = '';
      }
      // 重置预计佣金显示
      if (estimatedTarget) {
        estimatedTarget.content = '¥0.00';
      }

      console.log('回显为固定佣金模式:', {
        commissionType: 'fixed',
        commission: val.commission || 0.01,
      });
    }

    operate_id.value = val.spreaderGoodsId;
    title.value = L('设置佣金');
    type.value = 'set_commission';
    modalVisible.value = true;
  };

  //设置标签
  const setLabel = (goodsId) => {
    operateData.value = [];
    operateData.value = labelData.value;
    operate_id.value = goodsId;
    title.value = L('设置商品标签');
    type.value = 'set_label';
    modalVisible.value = true;
  };

  //关闭弹框
  const handleModalCancle = () => {
    operateData.value = [];
    modalVisible.value = false;
  };

  // 计算预计佣金
  const calculateEstimatedCommission = (rate) => {
    const productPrice = getCurrentProductPrice();
    if (!productPrice || !rate || rate <= 0) {
      return '¥0.00';
    }
    const estimatedCommission = (productPrice * rate) / 100;
    return `¥${estimatedCommission.toFixed(2)}`;
  };

  // 处理佣金类型变化
  const handleCommissionTypeChange = (event) => {
    if (event.contentItem.name === 'commissionType') {
      const newType = event.val1;
      console.log('佣金类型变化:', newType);

      // 获取字段引用
      let commissionTarget = operateData.value.find((item) => item.name === 'commission');
      let rateTarget = operateData.value.find((item) => item.name === 'commissionRate');
      let estimatedTarget = operateData.value.find((item) => item.name === 'estimatedCommission');

      if (newType === 'fixed') {
        // 切换到固定佣金模式
        if (rateTarget) {
          rateTarget.initialValue = undefined;
          rateTarget.value = '';
        }
        // 重置预计佣金显示
        if (estimatedTarget) {
          estimatedTarget.content = '¥0.00';
        }
        // 设置默认固定佣金值
        if (
          commissionTarget &&
          (!commissionTarget.initialValue || commissionTarget.initialValue <= 0)
        ) {
          commissionTarget.initialValue = 0.01;
        }
        console.log('切换到固定佣金模式');
      } else if (newType === 'rate') {
        // 切换到佣金比例模式
        if (commissionTarget) {
          commissionTarget.initialValue = undefined;
          commissionTarget.value = '';
        }
        // 重置佣金比例字段，不设置默认值
        if (rateTarget) {
          rateTarget.initialValue = undefined;
          rateTarget.value = '';
        }
        // 初始化预计佣金为0
        if (estimatedTarget) {
          estimatedTarget.content = '¥0.00';
        }
        console.log('切换到佣金比例模式');
      }
    } else if (event.contentItem.name === 'commissionRate') {
      // 佣金比例变化时，实时计算预计佣金
      const rate = event.val1;
      let estimatedTarget = operateData.value.find((item) => item.name === 'estimatedCommission');
      if (estimatedTarget) {
        estimatedTarget.content = calculateEstimatedCommission(rate);
      }
      console.log('佣金比例变化:', rate, '预计佣金:', calculateEstimatedCommission(rate));
    }
  };

  const sldConfirm = async (values) => {
    values.spreaderGoodsIds = operate_id.value;

    // 如果是设置佣金，需要根据佣金类型处理数据
    if (type.value === 'set_commission') {
      if (values.commissionType === 'fixed') {
        // 固定佣金模式，删除佣金比例字段
        delete values.commissionRate;
      } else {
        // 佣金比例模式，删除固定佣金字段
        delete values.commission;
      }
      // 删除佣金类型字段，因为接口不需要这个字段
      delete values.commissionType;
    }

    operateGoods(values, type.value);
  };

  const operateGoods = async (id, types_of) => {
    let param_data = {};
    let res;
    if (types_of == 'cancle') {
      param_data = { spreaderGoodsIds: id };
      res = await getSpreaderGoodsRemoveGoodsApi(param_data);
    } else if (types_of == 'set_label') {
      param_data = id;
      res = await getSpreaderGoodsBindLabelApi(param_data);
    } else if (types_of == 'set_commission') {
      param_data = id;
      res = await getSpreaderGoodsSetCommissionApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      handleModalCancle();
      type.value = '';
      reload();
    } else {
      failTip(res.msg);
    }
  };

  onMounted(() => {
    checkSpreaderState();
    get_label_list();
  });
</script>
<style lang="less" scoped>
  .business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
</style>
