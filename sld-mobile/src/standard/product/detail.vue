<template>
  <view :style="mix_diyStyle">
    <view class="container" v-if="isLoading" id="deContainer" @touchstart="dis = false">
      <!-- #ifndef MP -->
      <block v-if="!isNavShow">
        <view class="go_back" @click="goBack">
          <image :src="imgUrl + 'point/goods/go_back.png'"></image>
        </view>
        <view class="go_more" @tap="tipsShow">
          <image :src="imgUrl + 'goods_detail/more_tips.png'"></image>
          <block v-if="tips_show">
            <view class="triangle-up"> </view>
            <view class="tips">
              <button v-for="(item, index) in tips" :key="index" class="tips_pre" @tap="handleLink"
                :data-link="item.tips_link" :open-type="item.type" :data-type="item.type" plain="true">
                <image :src="item.tips_img"></image>
                <text>{{ item.tips_name }}</text>
              </button>
            </view>
          </block>
        </view>
      </block>
      <!-- #endif -->
      <!-- wx-5-start -->
      <!-- #ifdef MP -->
      <view class="search_wrap" :style="{ paddingTop: menuButtonTop }" v-if="!isNavShow">
        <view class="searchs search_mp" :style="{ height: menuButtonHeight, width: menuButtonleft }">
          <view class="search_mp_name" :style="{
            width: menuButtonWidth,
            height: menuButtonHeight,
            borderRadius: menuButtonHeight,
          }">
            <view class="more_tips_left" @click="goBack">
              <image :src="imgurl + 'fanhui1.png'" mode=""></image>
            </view>
            <view class="more_tips_xian"></view>
            <view class="more_tips">
              <image class="more" :src="imgurl + 'sanxian1.png'" @tap="tipsShow"></image>
              <block v-if="tips_show">
                <view class="triangle-up"> </view>
                <view class="tips">
                  <view v-for="(item, index) in tips" :key="index" class="tips_pre" @tap="handleLink"
                    :data-link="item.tips_link" :open-type="item.type" :data-type="item.type" plain="true">
                    <image :src="item.tips_img"></image>
                    <text>{{ item.tips_name }}</text>
                  </view>
                </view>
              </block>
            </view>
          </view>
          <view class="left" v-if="goodsData.state == 3 || activityState">
            <view class="flex_row_center_center favicon" @click="collectGoods">
              <text class="iconfont" :class="{
                iconyishoucang: goodsData.followGoods,
                iconweishoucang: !goodsData.followGoods,
              }"></text>
            </view>
            <view class="" style="margin-left: 30rpx">
              <image :src="imgurl + 'Share_btn.png'" mode="" @click="goShare"></image>
            </view>
          </view>
        </view>
      </view>
      <!-- #endif -->
      <!-- wx-5-end -->
      <!-- 透明遮罩层 -->
      <view v-if="transparent_mask" class="transparent_mask" :style="copyname_now ? 'opacity:0' : ''" @tap="hideMask">
      </view>
      <!-- app-1-start -->

      <!-- app-1-end -->
      <view class="nav_list_boxz" v-if="isNavShow" :style="{
        paddingTop: clients == 'mp' ? menuButtonHeights + 'px' : '',
        paddingLeft: clients == 'mp' ? '50rpx' : '',
      }">
        <!-- wx-6-start -->
        <!-- #ifdef MP -->
        <view class="search_wrap" :style="{ paddingTop: menuButtonTop }">
          <view class="searchs search_mp" :style="{ height: menuButtonHeight, width: menuButtonleft }">
            <view class="search_mp_name" :style="{
              width: menuButtonWidth,
              height: menuButtonHeight,
              borderRadius: menuButtonHeight,
            }">
              <view class="more_tips_left" @click="goBack">
                <image :src="imgurl + 'fanhui1.png'" mode=""></image>
              </view>
              <view class="more_tips_xian"></view>
              <view class="more_tips">
                <image class="more" :src="imgurl + 'sanxian1.png'" @tap="tipsShow"></image>
                <block v-if="tips_show">
                  <view class="triangle-up"> </view>
                  <view class="tips">
                    <view v-for="(item, index) in tips" :key="index" class="tips_pre" @tap="handleLink"
                      :data-link="item.tips_link" :open-type="item.type" :data-type="item.type" plain="true">
                      <image :src="item.tips_img"></image>
                      <text>{{ item.tips_name }}</text>
                    </view>
                  </view>
                </block>
              </view>
            </view>
            <view class="left" v-if="goodsData.state == 3 || activityState">
              <view class="flex_row_center_center favicon" @click="collectGoods">
                <text class="iconfont" :class="{
                  iconyishoucang: goodsData.followGoods,
                  iconweishoucang: !goodsData.followGoods,
                }"></text>
              </view>
              <view class="" style="margin-left: 30rpx">
                <image :src="imgurl + 'Share_btn.png'" mode="" @click="goShare"></image>
              </view>
            </view>
          </view>
        </view>
        <!-- #endif -->
        <!-- wx-6-end -->
        <view class="nav_list" :class="{ nav_list_no_opcity: noOpcity }">
          <!-- #ifndef MP -->
          <view class="go_back_nav flex_row_center_center" @click="goBack">
            <image :src="imgUrl + 'point/goods/back.png'"></image>
          </view>
          <!-- #endif -->
          <view class="nav_list_pre" v-if="recommendedList && recommendedList.length > 0"
            :class="{ nav_list_pre_active: currentNav == item.id }" v-for="(item, index) in navList" :key="index"
            @click="clickNav(item.id)">
            {{ item.text }}
          </view>
          <view class="nav_list_pre" v-if="recommendedList && recommendedList.length == 0"
            :class="{ nav_list_pre_active: currentNav == item.id }" v-for="(item, index) in navListNoRecommend"
            :key="index" @click="clickNav(item.id)">
            {{ item.text }}
          </view>
          <!-- #ifndef MP -->
          <view class="more_tips" @tap="tipsShow">
            <image class="more" :src="imgUrl + 'goods_detail/more.png'"></image>
            <block v-if="tips_show">
              <view class="triangle-up"> </view>
              <view class="tips">
                <view v-for="(item, index) in tips" :key="index" class="tips_pre" @tap="handleLink"
                  :data-link="item.tips_link" :open-type="item.type" :data-type="item.type" plain="true">
                  <image :src="item.tips_img"></image>
                  <text>{{ item.tips_name }}</text>
                </view>
              </view>
            </block>
          </view>
          <!-- #endif -->
        </view>
      </view>

      <!-- 添加顶部提示条 -->
      <view class="coupon_success_tip" v-if="showCouponTip">
        店铺优惠券领取成功
      </view>

      <!-- banner模块 start -->
      <productCarousel :goodsPics="defaultProduct && defaultProduct.goodsPics || []" :goodsVideo="goodsVideo"
        :imgUrl="imgUrl" :current.sync="currentSpecImageIndex" @play-video="toPlayPage">
      </productCarousel>
      <!-- banner模块 end -->

      <product-sec-kill :sec-kill-info="secKillInfo" :img-url="imgUrl" @remind="secKillPreview"
        @time-end="refreshSecKill" />

      <!-- 阶梯团活动start -->
      <view class="ladder" v-if="valiInfo(ladderInfo)">
        <ladderInfo :ladderInfo="ladderInfo" :ladderProcess="ladderProcess" @getGoodsDetail="getGoodsDetail">
        </ladderInfo>
      </view>
      <!-- 阶梯团活动end -->

      <!-- 预售 start -->
      <view v-if="valiInfo(preSellInfo) && preSellInfo.pre_run != 3">
        <preInfo :preSellInfo="preSellInfo" @getGoodsDetail="getGoodsDetail"></preInfo>
      </view>
      <!-- 预售 end -->

      <!-- 拼团start -->
      <view v-if="valiInfo(pinInfo)" class="pinInfo_box">
        <pinInfo :pinInfo="pinInfo" @getGoodsDetail="getGoodsDetail" ref="pinInfo"></pinInfo>
      </view>
      <!-- 拼团end -->

      <!-- 有活动商品样式 start -->
      <!-- <template>里不能够v-if用函数返回值做多个或判断 -->
      <view class="introduce_section_activity" v-if="activityState">
        <view class="activity_goods_des">
          <view class="activity_goods_name">
            {{ goodsData.goodsName }}
          </view>
          <view class="activity_share_collection">
            <view class="activity_goods_collection" @click="collectGoods">
              <text class="iconfont iconaixin1" v-if="goodsData.followGoods"></text>
              <text class="iconfont iconaixin" v-else></text>
              <text class="show_text">{{
                goodsData.followGoods ? $L("已收藏") : $L("收藏")
              }}</text>
            </view>
            <view class="activity_goods_share" @click="goShare">
              <text class="iconfont iconfenxiang"></text>
              <text class="show_text">{{ $L("分享") }}</text>
            </view>
            <view class="activity_goods_sales">
              {{ $L("已销") }}{{ goodsData.sales ? goodsData.sales : 0 }}
            </view>
          </view>
          <view class="activity_goods_brief" v-if="goodsData.goodsBrief">
            {{ goodsData.goodsBrief }}
          </view>
        </view>
      </view>
      <!-- 有活动商品样式 end -->

      <!-- 无活动商品 start-->
      <view class="introduce_section" v-else>
        <view class="price_part flex_row_between_center">
          <view class="flex_row_start_center">
            <view class="left">
              <block v-if="defaultProduct.superPrice && userCenterData.isSuper == 1">
                <view class="left_super flex_row_start_center">
                  <view class="sell_price">
                    <text class="unit">¥ </text>
                    <text class="price_int">{{
                      $getPartNumber(defaultProduct.superPrice, "int")
                    }}</text>
                    <text class="price_decimal">{{
                      $getPartNumber(defaultProduct.superPrice, "decimal")
                    }}</text>
                  </view>
                  <view class="left_super_price_img" :style="'background-image:url(' +
                    imgUrl +
                    'super/super_price.png)'
                    ">
                    会员价</view>
                  <view class="super_original_price" v-if="defaultProduct.productPrice" style="margin-left: 25rpx">
                    <text>{{ $L("") }}</text>
                    <text>
                      {{
                        $getPartNumber(defaultProduct.productPrice, "int")
                      }}</text>
                    <text>
                      {{
                        $getPartNumber(defaultProduct.productPrice, "decimal")
                      }}</text>
                  </view>
                </view>
              </block>
              <block v-else>
                <view class="sell_price" v-if="defaultProduct.productPrice">
                  <text class="unit">{{ $L("¥") }} </text>
                  <text class="price_int">{{
                    $getPartNumber(defaultProduct.productPrice, "int")
                  }}</text>
                  <text class="price_decimal">{{
                    $getPartNumber(defaultProduct.productPrice, "decimal")
                  }}</text>
                </view>
                <view class="original_price" v-if="defaultProduct.marketPrice && !defaultProduct.superPrice
                ">
                  {{ $L("¥") }} {{ filters.toFix(defaultProduct.marketPrice) }}
                </view>
              </block>
            </view>
            <view v-if="isSpreader" class="commission_info">
              <view class="commission_main">
                <text class="commission_label">分享赚</text>
                <text class="commission_value">¥{{ filters.toFix(commission_share + commission_invite) }}</text>
              </view>
              <view class="commission_detail">
                <text>分享{{ filters.toFix(commission_share) }}+绑粉{{ filters.toFix(commission_invite) }}</text> <text
                  style="margin-left:10px" @click="goSpreaderCenter">💰我的推广</text>
              </view>
            </view>
            <block v-if="userCenterData.isSuper != 1">
              <view v-if="defaultProduct.superPrice &&
                Number(defaultProduct.superPrice) > 0
              " class="left_super flex_row_start_center" style="margin-left: 30rpx">
                <view class="sell_price">
                  <text class="unit">¥ </text>
                  <text class="price_int">{{
                    $getPartNumber(defaultProduct.superPrice, "int")
                  }}</text>
                  <text class="price_decimal">{{
                    $getPartNumber(defaultProduct.superPrice, "decimal")
                  }}</text>
                </view>
                <view class="left_super_price_img" :style="'background-image:url(' + imgUrl + 'super/super_price.png)'
                  ">会员价
                </view>
              </view>
            </block>
          </view>
          <!-- #ifndef MP -->
          <view class="right flex_row_end_center" v-if="goodsData.state == 3">
            <view class="flex_column_center_center collection" @click="collectGoods">
              <text class="iconfont iconaixin1" v-if="goodsData.followGoods"></text>
              <text class="iconfont iconaixin" v-else></text>
              <text class="show_text">{{
                goodsData.followGoods ? $L("已收藏") : $L("收藏")
              }}</text>
            </view>
            <view class="flex_column_center_center" @click="goShare">
              <text class="iconfont iconfenxiang"></text>
              <text class="show_text">{{ $L("分享") }}</text>
            </view>
          </view>
          <!-- #endif -->
          <!-- wx-8-start -->
          <!-- #ifdef MP -->
          <view class="right flex_row_end_center">
            <view class="flex_column_center_center collection" @click="collectGoods" v-if="goodsData.state == 3">
              <text class="iconfont iconaixin1" v-if="goodsData.followGoods"></text>
              <text class="iconfont iconaixin" v-else></text>
              <text class="show_text">{{
                goodsData.followGoods ? $L("已收藏") : $L("收藏")
              }}</text>
            </view>
            <view class="flex_column_center_center" @click="goShare" v-if="goodsData.state == 3">
              <text class="iconfont iconfenxiang"></text>
              <text class="show_text">{{ $L("分享") }}</text>
            </view>
          </view>
          <!-- #endif -->
          <!-- wx-8-end -->
        </view>
        <block v-if="memberConfig.super_is_enable == 1">
          <view :class="userCenterData.isSuper
            ? 'flex_row_start_center'
            : 'flex_row_center_center'
            " v-if="saveAmount.totalAmount" class="price_super" :style="'background-image:url(' +
              imgUrl +
              (userCenterData.isSuper
                ? `super/goods_super.png`
                : `super/goods_normal.png`) +
              ')'
              ">
            <view :class="userCenterData.isSuper
              ? 'flex_row_start_center'
              : 'flex_row_center_center'
              " v-if="userCenterData.isSuper && saveAmount.totalAmount > 0" @click="superPopOpen">
              <image class="price_super_logo" :src="imgUrl + 'user/vip.png'" mode="aspectFit"></image>
              <view class="price_super_tag">超级会员</view>
              <view class="price_super_desc">
                预计此单可省
                <span>{{ Number(saveAmount.totalAmount).toFixed(2) }}</span>
                元
                <text class="iconfont iconziyuan11"></text>
              </view>
            </view>
            <view :class="userCenterData.isSuper
              ? 'flex_row_start_center'
              : 'flex_row_center_center'
              " v-else-if="!userCenterData.isSuper && saveAmount.totalAmount > 0">
              <view class="price_super_tips" @click.stop="superPopOpen">
                开通超级会员，预计此单可省
                <span>{{ Number(saveAmount.totalAmount).toFixed(2) }}</span>
                元
                <text class="iconfont iconziyuan11"></text>
              </view>
              <view class="price_super_arrow" @click="getSuper">></view>
            </view>
          </view>
        </block>

        <view class="flex_row_start_center" style="position: relative">
          <view class="goods_name" :class="{ widthIndent: goodsData.shopType == 2 }" @longpress="openCopyname"
            @touchend="copyOpenEnd">
            <view v-if="copyname_now" class="copy_pop" @click.stop.prevent="copyName">复制</view>
            <text>{{ goodsData.goodsName }}</text>
          </view>
          <view class="goods_sales">{{ $L("已销") }}{{ goodsData.sales ? goodsData.sales : 0 }}</view>
        </view>
        <view class="goods_ad" v-if="goodsData.goodsBrief">{{
          goodsData.goodsBrief
        }}</view>
      </view>
      <!-- 无活动商品 end -->

      <!-- 排行榜start -->
      <block v-if="rankData.length > 0">
        <gDRank :data="rankData"></gDRank>
      </block>
      <!-- 排行榜end -->

      <!-- 阶梯团 进度 start -->
      <view v-if="valiInfo(ladderInfo) &&
        ladderInfo.ruleList &&
        ladderInfo.ladder_run == 2
      ">
        <ladderProgress :ladderInfo="ladderInfo"></ladderProgress>
      </view>
      <!-- 阶梯团 进度 end -->

      <!-- 商品标题 end -->
    </view>

    <!-- 选择规格 start -->
    <view class="spec_con" @click="showSpecModel(goodsData.isVirtualGoods == 1 ? '' : 'buy')">
      <view class="spec_left">
        <view class="spec_left_title">{{ $L("规格") }}</view>
        <view class="spec_left_content" v-if="defaultProduct.getSpecValues">
          {{ defaultProduct.getSpecValues }}
        </view>
        <view class="spec_left_content" v-else>{{ $L("默认") }}</view>
      </view>
      <image :src="imgUrl + 'goods_detail/right_down.png'" class="spec_right"></image>
    </view>
    <!-- 选择规格 end -->

    <!-- 发货地址,及运费 start -->
    <view class="deliver_goods" @click="showAddress" v-if="goodsData.isVirtualGoods == 1">
      <view class="deliver_goods_con" v-if="(!curAddress || curAddress.length < 10) &&
        filters.toFix(expressFee) < 10000 &&
        goodsData.sales < 10000
      ">
        <view class="deliver_goods_left">
          <view class="deliver_goods_title">{{ $L("送至") }}</view>
          <view class="deliver_goods_address">
            <svgGroup type="location" width="17" height="19" :color="diyStyle_var['--color_main']">
            </svgGroup>
            <text :class="{ addressDefault: !curAddress }">{{
              curAddress ? curAddress : "请选择地址"
            }}</text>
          </view>
        </view>
        <view class="deliver_goods_center" v-if="expressFee != undefined">
          {{ $L("快递") }}:<span v-if="Number(expressFee) > 0">{{ filters.toFix(expressFee) }}{{ $L("元") }}</span><span
            v-else>{{ $L("免运费") }}</span>
        </view>
        <!-- #ifndef MP -->
        <!-- <view class="deliver_goods_right">{{ $L("已销") }}{{ goodsData.sales ? goodsData.sales : 0 }}</view> -->
        <!-- #endif -->
      </view>
      <view class="deliver_goods_con" v-else>
        <view class="deliver_goods_left">
          <view class="deliver_goods_title">{{ $L("送至") }}</view>
        </view>
        <view class="deliver_goods_right_main">
          <view class="deliver_goods_address">
            <svgGroup type="location" width="17" height="19" :color="diyStyle_var['--color_main']">
            </svgGroup>
            <text :class="{ addressDefault: !curAddress }">{{
              curAddress ? curAddress : "请选择地址"
            }}</text>
          </view>
          <view class="deliver_goods_right_bottom">
            <view class="deliver_goods_center">
              {{ $L("快递") }}:<span v-if="Number(expressFee) > 0">{{ filters.toFix(expressFee) }}{{ $L("元")
              }}</span><span v-else>{{ $L("免运费") }}</span>
            </view>
            <!-- #ifndef MP -->
            <view class="deliver_goods_right">{{ $L("已销") }}{{ goodsData.sales ? goodsData.sales : 0 }}
            </view>
            <!-- #endif -->
          </view>
        </view>
      </view>
    </view>
    <!-- 发货地址,及运费 end -->

    <!-- video推荐 start-->
    <!-- <product-recommend :recommendedVideoList="recommendedVideoList" :imgurl="imgurl" :goodsId="goodsId"
      :productId="productId" @goInformation="goInformation" @goProductDetail="goProductDetail" @goPublish="goPublish" /> -->
    <!-- video推荐 end -->

    <!-- 拼团成员列表start -->
    <view class="pin_group_con" v-if="valiInfo(pinInfo) && goodsData.state == 3">
      <pinGroupList :goodsId="pinInfo.goodsId" :spellId="pinInfo.spellId" @handleJoinGroup="handleJoinGroup"
        ref="pinGroup"></pinGroupList>
    </view>
    <!-- 拼团成员列表end -->

    <!-- 活动  start-->
    <view class="activity" v-if="(couponList && couponList.length > 0) ||
      (fullDisList && fullDisList.length > 0)
    ">
      <!-- 领券 start -->
      <view class="" v-if="couponList && couponList.length > 0">
        <view class="activity_coupons" @click="handleCouponClick">
          <view class="activity_coupons_left">
            <view class="activity_coupons_tips">{{ $L("活动") }}</view>
            <view class="activity_coupons_center">
              <template>
                <text class="activity_coupons_title" :class="{
                  activity_coupons_received:
                    isPopularityCard &&
                    (allCouponsReceived ||
                      couponList.every(
                        (coupon) =>
                          coupon.isReceive === 2 || coupon.isReceive === 3
                      )),
                }">
                  {{ getDisplayText }}
                </text>
                <!-- 只在非人气卡或未全部领取时显示优惠券列表 -->
                <view class="activity_coupons_list" v-if="!isPopularityCard ||
                  (!allCouponsReceived &&
                    !couponList.every(
                      (coupon) =>
                        coupon.isReceive === 2 || coupon.isReceive === 3
                    ))
                ">
                  <view class="activity_coupons_pre" :class="{
                    activity_coupons_pre_short:
                      item.couponContent.length > 10,
                  }" v-for="(item, index) in couponList" v-if="index < 2" :key="index">
                    <svgGroup type="to_youhuiquan" width="182" height="42" px="rpx"
                      :color="diyStyle_var['--color_main']" class="activity_coupons_pre_img">
                    </svgGroup>
                    {{ item.couponContent }}
                  </view>
                </view>
              </template>
            </view>
          </view>
        </view>
      </view>
      <!-- 领券 end -->
      <!-- 满优惠 start -->
      <view class="" v-if="fullDisList && fullDisList.length > 0" @click="openFullDisModel">
        <view :class="{
          full_discount: true,
          padd20: !(couponList && couponList.length > 0),
        }" v-for="(item, index) in fullDisList" :key="index">
          <view class="activity_coupons_tips" v-if="!(couponList && couponList.length > 0)">{{ $L("活动") }}
          </view>
          <block v-if="item.descriptionList.length">
            <text :class="{
              full_discount_title: true,
              discount_title_no_ma: !(couponList && couponList.length > 0),
            }">{{ item.promotionName }}</text>
            <view class="full_discount_list">
              <block v-for="(item1, index1) in item.descriptionList" :key="index1">
                <jyfParser :html="item1" style="width: 100%; overflow: hidden; flex-shrink: 0" :isAll="true">
                </jyfParser>
              </block>
            </view>
          </block>
        </view>
      </view>
      <!-- 满优惠 end -->
    </view>
    <!-- 活动  end-->

    <!-- 随机优惠券 start -->
    <view class="random_coupon" :class="{ hide: !rondomMod }">
      <view class="random_coupon_bg" :style="{
        backgroundImage: 'url(' + imgUrl + 'coupon/random_bg.png)',
      }" @click="goMyCoupon">
        <view class="random_coupon_price">￥{{ rondomDes.publishValue }}</view>
        <view class="random_coupon_des">{{ rondomDes.couponContent }}</view>
        <view class="close_btn" :style="{ backgroundImage: 'url(' + imgUrl + 'coupon/close.png)' }"
          @click.stop="closeCoupon"></view>
      </view>
    </view>
    <!-- 随机优惠券 end -->

    <!-- 优惠券弹框 start -->
    <uni-popup ref="couponModel" type="bottom" @touchmove.stop.prevent="moveHandle" class="popup_container">
      <view class="coupon_model">
        <view class="coupon_model_title">
          <text>{{ $L("领取优惠券") }}</text>
          <image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" @click="closeModel"></image>
        </view>
        <scroll-view class="coupon_model_list" scroll-y="true">
          <view class="my_coupon_pre" v-for="(item, index) in couponList" :key="index">
            <view class="coupon_pre_top">
              <image :src="imgUrl + 'coupon/coupon_pre_img_bg.png'" mode="" class="coupon_pre_top_bg_img">
              </image>
              <svgGroup type="to_youhuiquanjuchi" width="190" height="190" px="rpx" class="to_youhuiquan"
                v-if="item.isReceive == 1" :color="diyStyle_var['--color_coupon_main']">
              </svgGroup>
              <svgGroup type="to_youhuiquanjuchi" width="190" height="190" px="rpx" class="to_youhuiquan"
                v-if="item.isReceive == 2" :color="diyStyle_var['--color_coupon_opacity']">
              </svgGroup>
              <svgGroup type="to_youhuiquanjuchi" width="190" height="190" px="rpx" class="to_youhuiquan"
                v-if="item.isReceive == 3" color="#dddddd">
              </svgGroup>
              <view class="coupon_pre_left">
                <!-- 固定券 start -->
                <view class="coupon_pre_price" :class="{
                  coupon_pre_price_high:
                    item.publishValue.toString().length > 6,
                }" v-if="item.couponType == 1">
                  <text class="unit">{{ $L("¥") }} </text>
                  <text class="price_int" v-if="item.publishValue.toString().indexOf('.') != -1 &&
                    item.publishValue.toString().split('.')[1] > 0
                  ">{{ item.publishValue }}</text>
                  <text class="price_int" v-else>{{
                    $getPartNumber(item.publishValue, "int")
                  }}</text>
                </view>
                <!-- 固定券 end -->
                <!-- 折扣券 start -->
                <view class="coupon_pre_price" v-if="item.couponType == 2">
                  <view class=""></view>
                  <text class="price_int">{{
                    filters.toSplit(filters.toFixNum(item.publishValue, 1))[0]
                  }}</text>.
                  <text class="price_decimal">{{
                    filters.toSplit(filters.toFixNum(item.publishValue, 1))[1]
                  }}</text>
                  <text class="price_decimal">{{ $L("折") }}</text>
                </view>
                <!-- 折扣券 end -->
                <!-- 随机券 start -->
                <view class="coupon_pre_price" v-if="item.couponType == 3">
                  <text class="unit">{{ $L("¥") }} </text>
                  <text class="price_int">{{
                    filters.toSplit(filters.toFixNum(item.randomMax, 1))[0]
                  }}</text>.
                  <text class="price_decimal">{{
                    filters.toSplit(filters.toFixNum(item.randomMax, 1))[1]
                  }}</text>
                </view>
                <!-- 随机券 end -->
                <!-- 运费券 start -->
                <view class="coupon_pre_price" v-if="item.couponType == 4">
                  <block v-if="item.publishValue">
                    <text class="unit">{{ $L("¥") }} </text>
                    <text class="price_int" v-if="item.publishValue.toString().indexOf('.') != -1 &&
                      item.publishValue.toString().split('.')[1] > 0
                    ">{{ item.publishValue }}</text>
                    <text class="price_int" v-else>{{
                      $getPartNumber(item.publishValue, "int")
                    }}</text>
                  </block>
                  <block v-else>
                    <text style="font-size: 30rpx">免运费</text>
                  </block>
                </view>
                <!-- 运费 end -->
                <view class="coupon_pre_active">{{
                  item.couponContent
                }}</view>
              </view>
              <view class="coupon_pre_cen">
                <view class="coupon_pre_title">{{ item.useTypeValue }}</view>
                <view class="coupon_pre_time">{{ item.effectiveStart }}~{{ item.effectiveEnd }}
                </view>
                <view class="coupon_pre_rules" @click="descriptionOpen(item.couponId)">
                  <text>{{ $L("使用规则") }}</text>
                  <image :src="item.isOpen
                    ? imgUrl + 'coupon/up.png'
                    : imgUrl + 'coupon/down.png'
                    " mode="aspectFit">
                  </image>
                </view>
              </view>
              <view class="coupon_pre_right grey" v-if="item.isReceive == 3">{{ $L("已抢完") }}</view>
              <view class="coupon_pre_right grey" v-else-if="item.isReceive == 2">{{ $L("已领") }}
              </view>
              <view class="coupon_pre_right" v-else-if="item.isReceive == 1" @click="goReceive(item)">
                {{ $L("立即领取") }}
              </view>
            </view>
            <view class="coupon_rules" v-if="item.isOpen == true">
              <view class="coupon_rules_title">{{ $L("使用规则") }}:{{ item.description }}</view>
            </view>
            <view class="coupon_type">{{ item.couponTypeValue }}</view>
            <view class="coupon_progress" v-if="item.isReceive != 3" :class="{ opa: item.isReceive == 2 }">
              {{ $L("已抢") }}{{ item.receivePercent }}%
              <view class="progress_con">
                <progress :percent="item.receivePercent" stroke-width="3" activeColor="#FFFFFF" :backgroundColor="item.isReceive == 2
                  ? diyStyle_var['--color_coupon_opacity']
                  : diyStyle_var['--color_coupon_main']
                  " border-radius="2px" />
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
    <!-- 优惠券弹框 end -->

    <!-- 满优惠弹框 start -->
    <uni-popup ref="fullDisModel" type="bottom" @touchmove.stop.prevent="moveHandle">
      <view class="fulldis_model">
        <view class="fulldis_model_title">
          <text>{{ $L("满优惠") }}</text>
          <image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" @click="closeModel"></image>
        </view>
        <scroll-view class="fulldis_model_list" scroll-y="true">
          <view v-for="(item, index) in fullDisList" :key="index">
            <view class="fulldis_model_pre" v-for="(item1, index1) in item.descriptionList" :key="index1">
              <view class="fulldis_pre_tips"></view>
              <view class="fulldis_pre_con">
                <jyfParser :html="item1" :isAll="true"></jyfParser>
              </view>
            </view>
          </view>
        </scroll-view>
        <view class="full_dis_tips">
          {{ $L("以上优惠仅为初步预估，实际以结算最终价格为准！") }}
        </view>
      </view>
    </uni-popup>
    <!-- 满优惠弹框 end -->

    <!-- 服务 start -->
    <!-- <view class="service" v-if="goodsData &&
      goodsData.serviceLabels &&
      goodsData.serviceLabels.length > 0
      " @click="serviceModel">
      <view class="service_left">
        <view class="service_title">
          {{ $L("服务") }}
        </view>
        <view class="service_con">
          <view class="service_pre" v-for="(item, index) in goodsData.serviceLabels" :key="index" v-if="index < 3">
            <text class="service_pre_tips"></text>
            <text>{{ item.labelName }}</text>
          </view>
        </view>
      </view>
      <view class="service_right">
        <image :src="imgUrl + 'goods_detail/right_down.png'" mode="aspectFit"></image>
      </view>
    </view> -->
    <!-- 服务 end -->

    <!-- 服务弹框 start -->
    <uni-popup ref="serviceModel" type="bottom" @touchmove.stop.prevent="moveHandle">
      <view class="service_model">
        <view class="service_model_top">
          <text>{{ $L("服务") }}</text>
          <image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" @click="closeModel"></image>
        </view>
        <scroll-view class="uni-list service_model_list" scroll-y="true">
          <view class="service_list_pre" v-for="(item, index) in goodsData.serviceLabels" :key="index">
            <view class="service_list_title">{{ item.labelName }}</view>
            <view class="service_list_des">{{ item.description }}</view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
    <!-- 服务弹框 end -->



    <!-- 评价 start-->
    <!-- <view class="eva_section" id="nav2">
      <view class="e_header flex_row_between_center">
        <view class="left flex_row_start_end">
          <text class="tit">{{ $L("商品评价") }}</text>
          <text class="e_num" v-if="goodsCommentsInfo && goodsCommentsInfo.commentsCount">({{
            goodsCommentsInfo.commentsCount }})</text>
          <text class="e_rate" v-if="goodsCommentsInfo && goodsCommentsInfo.highPercent">{{
            goodsCommentsInfo.highPercent != "0%"
            ? $L("好评率") + goodsCommentsInfo.highPercent
            : $L("暂无好评")
          }}</text>
        </view>
        <view class="right flex_row_end_center" @click="goEvaluation">
          <text class="view_more">{{ $L("查看更多") }}</text>
          <image :src="imgUrl + 'goods_detail/right_down.png'" mode="aspectFit" class="right_down">
          </image>
        </view>
      </view>
      <view class="eva_box flex_column_start_start" v-if="goodsCommentsInfo &&
        goodsCommentsInfo.list &&
        goodsCommentsInfo.list.length > 0
        ">
        <view class="e_member_info flex_row_start_center">
          <image class="portrait" :src="goodsCommentsInfo.list[0].memberAvatar" mode="aspectFill">
          </image>
          <text class="name">{{
            filters.toAnonymous(goodsCommentsInfo.list[0].memberName)
          }}</text>
          <uni-rate :readonly="true" :value="goodsCommentsInfo.list[0].score" active-color="var(--color_main)"
            disabledColor="#ccc" :size="18" />
        </view>
        <text class="con" v-if="goodsCommentsInfo.list[0].content">{{
          goodsCommentsInfo.list[0].content
        }}</text>
        <text class="con" v-else>{{
          goodsCommentsInfo.list[0].score >= 4
          ? $L("好评")
          : goodsCommentsInfo.list[0].score < 2 ? $L("差评") : $L("中评") }}</text>
      </view>
    </view> -->
    <!-- 评价end -->

    <!-- 店铺 start -->
    <view class="shop">
      <view class="shop_des" @click="goShopHome()">
        <view class="shop_des_image">
          <image :src="storeInf.storeLogo" mode="aspectFit" class=""></image>
        </view>
        <view class="shop_des_con">
          <view class="shop_con_title">
            <span style="position: relative">
              {{ storeInf.storeName }}
            </span>
          </view>

          <!-- <view class="shop_con_type">
            <view class="shop_type" v-if="storeInf.isOwnStore == 1">{{
              $L("自营")
            }}</view>
            <view class="shop_follow_num">
              {{ storeInf.followNumber ? storeInf.followNumber : 0
              }}{{ $L("人关注") }}</view>
          </view> -->
        </view>
      </view>
      <!-- <view class="shop_des_list">
        <view class="shop_des_pre">
          <text>{{ $L("描述相符") }}</text>
          <text>{{
            storeInf.descriptionScore
            ? filters.toFixNum(storeInf.descriptionScore, 1)
            : "--"
          }}{{ $L("分") }}</text>
          <image :src="imgUrl + 'goods_detail/low.png'" mode="aspectFit" v-if="storeInf.descriptionScore < 2">
          </image>
          <view class="shop_des_pre_img" v-else>
            {{ storeInf.descriptionScore > 4 ? "高" : "中" }}
          </view>
        </view>
        <view class="shop_des_pre">
          <text>{{ $L("服务态度") }}</text>
          <text>{{
            storeInf.serviceScore
            ? filters.toFixNum(storeInf.serviceScore, 1)
            : "--"
          }}{{ $L("分") }}</text>
          <image :src="imgUrl + 'goods_detail/low.png'" mode="aspectFit" v-if="storeInf.serviceScore < 2">
          </image>
          <view class="shop_des_pre_img" v-else>
            {{ storeInf.serviceScore > 4 ? "高" : "中" }}
          </view>
        </view>
        <view class="shop_des_pre">
          <text>{{ $L("发货速度") }}</text>
          <text>{{
            storeInf.deliverScore
            ? filters.toFixNum(storeInf.deliverScore, 1)
            : "--"
          }}{{ $L("分") }}</text>
          <image :src="imgUrl + 'goods_detail/low.png'" mode="aspectFit" v-if="storeInf.deliverScore < 2">
          </image>
          <view class="shop_des_pre_img" v-else>
            {{ storeInf.deliverScore > 4 ? "高" : "中" }}
          </view>
        </view>
      </view> -->
      <!-- <view class="shop_links">
        <image :src="imgUrl + 'goods_detail/contact_service.png'" mode="aspectFit" @click="toKefu"></image>
        <image :src="imgUrl + 'goods_detail/go_store.png'" mode="aspectFit" @click="goShopHome()"></image>
      </view> -->
    </view>
    <!-- 铺 end -->

    <!-- 店铺推荐 start-->
    <!-- <view class="store_recommend" id="nav3" v-if="recommendedList && recommendedList.length > 0">
      <view class="store_recommend_top">
        <view class="store_recommend_title">{{ $L("店铺推荐") }}</view>
      </view>
      <view class="store_recommend_list">
        <view class="store_recommend_pre" v-for="(item, index) in recommendedList" :key="index"
          @click="goProductDetail(item.defaultProductId)" v-if="index < 6">
          <view class="store_reco_pre_image">
            <view class="image" :style="'background-image:url(' + item.goodsImage + ')'"></view>
          </view>
          <view class="store_reco_pre_name">{{ item.goodsName }}</view>
          <view class="store_reco_pre_price">{{ item.goodsPrice }}</view>
        </view>
      </view>
    </view> -->
    <!-- 店铺推荐 end -->

    <!-- 规格参数 start -->
    <view class="spec_param" v-if="goodsParameterList && goodsParameterList.length > 0">
      <view class="spec_param_title">
        <text>{{ $L("规格参数") }}</text>
        <view class="image" :style="'background-image:url(' +
          imgUrl +
          'goods_detail/spec_param_bg.png)'
          ">
        </view>
      </view>
      <view class="spec_param_list" :class="{ open_param: openGoodsParam }">
        <view class="spec_param_pre" v-for="(item, index) in goodsParameterList" :key="index">
          <view>{{ item.parameterName }}</view>
          <view>{{ item.parameterValue }}</view>
        </view>
      </view>
      <view class="spec_param_fold" @click="handleGoodsParam" v-if="goodsParameterList.length > 5">
        <text>{{ openGoodsParam ? $L("收起") : $L("展开") }}</text>
        <image :src="openGoodsParam
          ? imgUrl + 'goods_detail/up.png'
          : imgUrl + 'goods_detail/down.png'
          " mode="aspectFit">
        </image>
      </view>
    </view>
    <!-- 规格参数 end -->

    <view class="detail-desc" id="nav4">
      <!-- <view class="detail-desc_title">
        <text>{{ $L("商品详情") }}</text>
        <view class="image" :style="'background-image:url(' +
          imgUrl +
          'goods_detail/spec_param_bg.png)'
          ">
        </view>
      </view> -->
      <jyf-parser v-if="goodsData.topTemplateContent != undefined &&
        goodsData.topTemplateContent
      " :pHidden="false" :isAll="true" :html="goodsData.topTemplateContent" ref="description"></jyf-parser>
      <jyf-parser v-if="goodsData.goodsDetails != undefined && goodsData.goodsDetails" :pHidden="false" :isAll="true"
        :html="goodsData.goodsDetails" ref="description"></jyf-parser>
      <jyf-parser v-if="goodsData.bottomTemplateContent != undefined &&
        goodsData.bottomTemplateContent
      " :pHidden="false" :isAll="true" :html="goodsData.bottomTemplateContent" ref="description">
      </jyf-parser>
    </view>

    <!-- 版本号展示 -->
    <view class="version_info">
      v{{ appVersion }}#{{currentMemberU}}#{{ lastSpreaderU }}
    </view>


    <!-- 推荐商品start -->
    <view class="recommendGoods_wrap">
      <recommendGoods ref="recommendGoods" @loadedFn="recommendGoodsLoaded"></recommendGoods>
    </view>
    <!-- 推荐商品start -->


    <!-- 底部操作菜单 -->
    <view class="page_bottom">
      <!-- 新增回首页按钮 -->
      <view class="p_b_btn" @click="goHome">
        <image :src="imgUrl + 'goods_detail/home.png'" mode="aspectFit"></image>
        <text class="show_text">{{ $L("回首页") }}</text>
      </view>

      <view class="p_b_btn" @click="goShopHome">
        <image :src="imgUrl + 'goods_detail/store.png'" mode="aspectFit"></image>
        <text class="show_text">{{ $L("店铺") }}</text>
      </view>

      <view url="/pages/index/index" open-type="switchTab" @click="toKefu" class="p_b_btn">
        <image :src="imgUrl + 'goods_detail/service.png'" mode="aspectFit" class="service_img"></image>
        <text class="show_text">{{ $L("客服") }}</text>
      </view>

      <navigator url="/pages/cart/cart" open-type="switchTab" class="p_b_btn">
        <image :src="imgUrl + 'goods_detail/cart.png'" mode="aspectFit" class="cart_img"></image>
        <text class="show_text">{{ $L("购物车") }}</text>
        <text class="cart_num" v-if="cartNumShow && cartNum > 0">{{
          cartNum
        }}</text>
      </navigator>

      <!-- 活动商品 start -->
      <block v-if="valiInfo(secKillInfo) && secKillInfo.state == 2">
        <!-- 秒杀活动  立即秒杀 已抢完 start -->
        <view class="action_btn_group">
          <view class="action_btn instant_second_kill flex_row_center_center" @click="showSpecModel"
            v-if="defaultProduct.productStock != 0">
            {{ $L("立即秒杀") }}
          </view>
          <view class="action_btn seckill_finished flex_row_center_center" @click="showSpecModel" v-else>
            {{ $L("已抢完") }}
          </view>
        </view>
        <!-- 秒杀活动 立即秒杀 已抢完 end -->
      </block>

      <block v-else-if="valiInfo(preSellInfo) && preSellInfo.pre_run != 1">
        <!-- 预售活动 立即付定金，全款支付 start -->
        <view class="action_btn_group">
          <block v-if="defaultProduct.productStock == 0">
            <view class="action_btn not_stock flex_row_center_center" @click="showSpecModel">
              {{ $L("库存不足") }}
            </view>
          </block>
          <block v-else>
            <view class="action_btn preSale_btn_deposit flex_row_center_center" @click="showSpecModel">
              {{ preSellInfo.type == 1 ? $L("立即付定金") : $L("立即支付") }}
            </view>
          </block>
        </view>
      </block>

      <!-- 其他活动商品按钮组保持不变 -->
      <!-- ... -->

      <!-- 正常商品 start -->
      <block v-else>
        <!--库存不足start -->
        <view class="action_btn_group" v-if="defaultProduct && defaultProduct.productStock == 0">
          <view class="action_btn not_stock flex_row_center_center" @click="showSpecModel">
            {{ $L("库存不足") }}
          </view>
        </view>
        <!--库存不足 end -->
        <!-- 普通商品 start -->
        <view class="action_btn_group" v-else>
          <block v-if="goodsData.isVirtualGoods == 1">
            <!-- 虚拟商品的按钮保持不变 -->
            <view class="action_btn add_cart_btn flex_row_center_center" @click="showSpecModel('add')">
              {{ $L("加入购物车") }}
            </view>
            <view class="action_btn buy_now_btn flex_row_center_center" @click="showSpecModel('buy')">
              {{ $L("立即购买") }}
            </view>
          </block>
          <block v-else>
            <!-- 只判断是否为推手,不再判断是否为推手商品 -->
            <block v-if="isSpreader">
              <view class="action_btn buy_now_btn flex_row_center_center"
                style="width:280rpx;border-radius:35rpx 0 0 35rpx;" @click="showSpecModel('buy')">
                {{ $L("自购省") }}
              </view>
              <view class="action_btn share_earn_btn flex_row_center_center"
                style="width:280rpx;border-radius:0 35rpx 35rpx 0;background:var(--color_vice);" @click="goShare">
                {{ $L("分享赚") }}
              </view>
            </block>
            <block v-else>
              <view class="action_btn virtual_buy flex_row_center_center" @click="showSpecModel('buy')">
                {{ $L("立即购买") }}
              </view>
            </block>
          </block>
        </view>
        <!-- 普通商品 end -->
      </block>
      <!-- 正常商品 end -->
    </view>

    <!-- 分享 -->
    <view class="share_container">
      <goodsShare ref="goodsShare"></goodsShare>
    </view>
    <!-- 分享 -->

    <!-- 规格弹框 start -->
    <uni-popup class="spec_model" ref="specModel" type="bottom">
      <view class="spec_model_con">
        <view class="spec_model_content">
          <view class="spec_model_top">
            <view class="spec_model_goods">
              <view class="spec_goods_image">
                <!-- 修改这里,使用当前选中规格对应的商品图片 -->
                <image
                  :src="defaultProduct && defaultProduct.goodsPics && defaultProduct.goodsPics[currentSpecImageIndex || 0]"
                  mode="aspectFit"
                  @click="previewSpecImage(defaultProduct && defaultProduct.goodsPics && defaultProduct.goodsPics[currentSpecImageIndex || 0])">
                </image>
              </view>

              <view class="spec_goods_right">
                <view class="spec_goods_price_con">
                  <view class="spec_prices">
                    <!-- 立即秒杀进行中 start -->
                    <view class="spec_goods_price spec_goods_price_seck" v-if="secKillInfo && secKillInfo.state == 2">
                      <text>{{ $L("￥") }}</text>
                      <text>{{
                        $getPartNumber(secKillInfo.seckillPrice, "int")
                      }}</text>
                      <text>{{
                        $getPartNumber(secKillInfo.seckillPrice, "decimal")
                      }}</text>
                      <!-- 添加秒杀原价 -->
                      <text class="original_price">{{ $L("¥") }}
                        {{ filters.toFix(defaultProduct.productPrice) }}</text>
                    </view>
                    <!-- 立即秒杀进行中 end -->

                    <!-- 预售 start -->
                    <!-- 立即付定金 start -->
                    <view class="spec_goods_price" :class="{
                      spec_goods_price_pre:
                        valiInfo(preSellInfo) && preSellInfo.pre_run != 1,
                    }" v-else-if="preSellInfo &&
                      preSellInfo.type == 1 &&
                      preSellInfo.pre_run == 2
                    ">
                      <text>{{ $L("￥") }}</text>
                      <text>{{
                        $getPartNumber(preSellInfo.firstMoney, "int")
                      }}</text>
                      <text>{{
                        $getPartNumber(preSellInfo.firstMoney, "decimal")
                      }}</text>
                    </view>
                    <!-- 立即付定金 end -->
                    <!-- 全款 start -->
                    <view class="spec_goods_price" :class="{
                      spec_goods_price_pre:
                        valiInfo(preSellInfo) && preSellInfo.pre_run != 1,
                    }" v-else-if="preSellInfo &&
                      preSellInfo.type == 2 &&
                      preSellInfo.pre_run == 2
                    ">
                      <text>{{ $L("￥") }}</text>
                      <text>{{
                        $getPartNumber(preSellInfo.presellPrice, "int")
                      }}</text>
                      <text>{{
                        $getPartNumber(preSellInfo.presellPrice, "decimal")
                      }}</text>
                    </view>
                    <!-- 全款 end -->
                    <!-- 预售 end -->

                    <!-- 阶梯团start -->
                    <view class="spec_goods_price spec_goods_price_lad" v-else-if="JSON.stringify(ladderInfo) != '{}'">
                      <text>{{ $L("￥") }}</text>
                      <text>{{
                        $getPartNumber(ladderInfo.advanceDeposit, "int")
                      }}</text>
                      <text>{{
                        $getPartNumber(ladderInfo.advanceDeposit, "decimal")
                      }}</text>
                    </view>
                    <!-- 阶团end -->

                    <!-- 拼团start -->
                    <view class="spec_goods_price spec_goods_price_pin" v-else-if="JSON.stringify(pinInfo) != '{}' && pinButState
                    ">
                      <text>{{ $L("￥") }}</text>
                      <text>{{
                        $getPartNumber(
                          pinInfo.leaderPrice
                            ? pinButState == 3
                              ? pinInfo.spellPrice
                              : pinInfo.leaderPrice
                            : pinInfo.spellPrice,
                          "int"
                        )
                      }}</text>
                      <text>{{
                        $getPartNumber(
                          pinInfo.leaderPrice
                            ? pinButState == 3
                              ? pinInfo.spellPrice
                              : pinInfo.leaderPrice
                            : pinInfo.spellPrice,
                          "decimal"
                        )
                      }}</text>
                    </view>
                    <!-- 拼团end -->

                    <!-- 正常商品start -->

                    <view class="flex_row_start_center" v-else>
                      <block v-if="defaultProduct.superPrice &&
                        userCenterData.isSuper == 1
                      ">
                        <view class="spec_goods_price left_super flex_row_start_center">
                          <view class="sell_price">
                            <text class="unit">¥ </text>
                            <text class="price_int">{{
                              $getPartNumber(defaultProduct.superPrice, "int")
                            }}</text>
                            <text class="price_decimal">{{
                              $getPartNumber(
                                defaultProduct.superPrice,
                                "decimal"
                              )
                            }}</text>
                          </view>
                          <view class="left_super_price_img" :style="'background-image:url(' +
                            imgUrl +
                            'super/super_price.png)'
                            ">
                            会员价
                          </view>
                          <!-- 添加会员价原价 -->
                          <view class="original_price" v-if="defaultProduct.productPrice">
                            {{ $L("¥") }}
                            {{ filters.toFix(defaultProduct.productPrice) }}
                          </view>
                        </view>
                      </block>
                      <block v-else>
                        <view class="spec_goods_price">
                          <text :class="{
                            address_pin:
                              valiInfo(pinInfo) && pinInfo.state == 1,
                            address_pre:
                              valiInfo(preSellInfo) &&
                              preSellInfo.pre_run != 1,
                            address_lad: valiInfo(ladderInfo),
                            address_seck:
                              secKillInfo && secKillInfo.state == 2,
                          }">{{ $L("￥") }}</text>
                          <text :class="{
                            address_pin:
                              valiInfo(pinInfo) && pinInfo.state == 1,
                            address_pre:
                              valiInfo(preSellInfo) &&
                              preSellInfo.pre_run != 1,
                            address_lad: valiInfo(ladderInfo),
                            address_seck:
                              secKillInfo && secKillInfo.state == 2,
                          }">{{
                            $getPartNumber(
                              defaultProduct.productPrice,
                              "int"
                            )
                          }}</text>
                          <text :class="{
                            address_pin:
                              valiInfo(pinInfo) && pinInfo.state == 1,
                            address_pre:
                              valiInfo(preSellInfo) &&
                              preSellInfo.pre_run != 1,
                            address_lad: valiInfo(ladderInfo),
                            address_seck:
                              secKillInfo && secKillInfo.state == 2,
                          }">{{
                            $getPartNumber(
                              defaultProduct.productPrice,
                              "decimal"
                            )
                          }}</text>
                          <!-- 添加普通商品原价 -->
                          <view class="original_price" v-if="defaultProduct.marketPrice &&
                            !defaultProduct.superPrice
                          ">
                            {{ $L("¥") }}
                            {{ filters.toFix(defaultProduct.marketPrice) }}
                          </view>
                        </view>
                      </block>

                      <view v-if="defaultProduct.superPrice &&
                        userCenterData.isSuper != 1
                      " class="left_super flex_row_start_center">
                        <view class="sell_price">
                          <text class="unit">¥ </text>
                          <text class="price_int">{{
                            $getPartNumber(defaultProduct.superPrice, "int")
                          }}</text>
                          <text class="price_decimal">{{
                            $getPartNumber(
                              defaultProduct.superPrice,
                              "decimal"
                            )
                          }}</text>
                        </view>
                        <view class="left_super_price_img" :style="'background-image:url(' +
                          imgUrl +
                          'super/super_price.png)'
                          ">
                          会员价
                        </view>
                      </view>
                    </view>
                    <!-- 正常商品end -->
                  </view>
                  <!-- 添加佣金显示区域 -->
                  <view v-if="isSpreader" class="spec_commission_info">
                    <view class="spec_commission_main">
                      <text class="spec_commission_label">分享赚</text>
                      <text class="spec_commission_value">¥{{ filters.toFix(commission_share + commission_invite) }}</text>
                    </view>
                    <view class="commission_detail">
                      <text>分享{{ filters.toFix(commission_share) }}+绑粉{{ filters.toFix(commission_invite) }}</text> <text
                        style="margin-left:10px" @click="goSpreaderCenter">💰我的推广</text>
                    </view>
                  </view>
                  <!-- 活动标识 start -->
                  <view class="sec_kill_tips" v-if="secKillInfo && secKillInfo.state == 2">
                    {{ $L("限时秒杀") }}
                  </view>
                  <view class="pre_sale_tips" v-if="JSON.stringify(preSellInfo) != '{}' &&
                    preSellInfo.pre_run == 2
                  ">
                    {{ $L("售") }}
                  </view>
                  <text class="ladder_regiment_tips" v-if="JSON.stringify(ladderInfo) != '{}'">{{ $L("阶梯团") }}</text>
                  <text class="pin_tips" v-if="JSON.stringify(pinInfo) != '{}' && pinButState">{{ $L("拼团") }}</text>
                  <!-- 活动标识 end -->
                </view>
                <!-- 已下架商品 start -->
                <view class="spec_goods_des" v-if="goodsData.state != 3">
                  {{ $L("商品已下架") }}
                </view>
                <!-- 已下架商品 end -->
                <!-- 普通商品 start -->
                <view class="spec_goods_des" v-else>
                  {{ $L("已选规格") }}：
                  <text v-if="defaultProduct.getSpecValues">{{
                    defaultProduct.getSpecValues
                  }}</text>
                  <text v-else>{{ $L("默认") }}</text>
                </view>
                <!-- 普通商品 end -->
              </view>
            </view>
            <image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" class="close_spec" @click="closeSpecModel">
            </image>
          </view>
          <scroll-view scroll-y="true" class="spec_content">
            <view class="spec_list" v-if="specs && specs.length > 0">
              <view class="spec_list_pre" v-for="(item, index) in specs" :key="index">
                <view class="spec_list_pre_name">{{ item.specName }}</view>
                <block v-if="item &&
                  item.specValueList &&
                  item.specValueList.length > 0
                " v-for="(item1, index1) in item.specValueList" :key="index1">
                  <!-- checkState : 1-选中，2-可选，3-禁用 -->
                  <view class="spec_list_pre_desc" :class="{
                    spec_list_pre_desc_disabled: item1.checkState == '3',
                  }" v-if="item1.checkState == '3'">
                    <view class="spec_list_pre_con">
                      <image :src="item1.image" mode="aspectFit" v-if="item1.image">
                      </image>
                      <text>{{ item1.specValue }}</text>
                    </view>
                  </view>
                  <view class="spec_list_pre_desc" :class="{
                    spec_list_pre_desc_active: item1.checkState == '1',
                    spec_list_pre_desc_active_pin:
                      valiInfo(pinInfo) &&
                      item1.checkState == '1' &&
                      pinInfo.state == 1,
                    spec_list_pre_desc_active_pre:
                      valiInfo(preSellInfo) &&
                      preSellInfo.pre_run != 1 &&
                      item1.checkState == '1',
                    spec_list_pre_desc_active_lad:
                      item1.checkState == '1' && valiInfo(ladderInfo),
                    spec_list_pre_desc_active_seck:
                      item1.checkState == '1' &&
                      secKillInfo &&
                      secKillInfo.state == 2,
                  }" @click="
                    selectSpecVal('choice', item.specId, item1.specValueId)
                    " v-else>
                    <view class="spec_list_pre_con">
                      <image :src="item1.image" mode="aspectFit" v-if="item1.image">
                      </image>
                      <text>{{ item1.specValue }}</text>
                    </view>
                  </view>
                </block>
              </view>
            </view>
            <view class="spec_num">
              <view class="spec_num_left">
                {{ $L("购买数量") }}
              </view>
              <view class="">
                <view class="spec_num_right">
                  <text @click="editNum('reduce')" :class="{ no_edit: currentSpecNum == 1 }">-</text>
                  <view class="spec_num_input_wrap">
                    <input type="number" v-model="currentSpecNum" @blur="editNum('edit', $event)" cursor-spacing="0"
                      :cursor="currentSpecNum.toString().length" maxlength="5" />
                  </view>
                  <text @click="editNum('add')" :class="{ no_edit: noEdit }">+</text>
                </view>
                <view v-if="actiState">
                  <text class="buyLimit" v-if="JSON.stringify(preSellInfo) != '{}' &&
                    preSellInfo.buyLimit > 0
                  ">{{ $L("限购数量") }}{{ preSellInfo.buyLimit
                  }}{{ $L("件") }}</text>
                  <text class="buyLimit" v-if="JSON.stringify(ladderInfo) != '{}' &&
                    ladderInfo.buyLimit > 0
                  ">{{ $L("限购数量") }}{{ ladderInfo.buyLimit
                  }}{{ $L("件") }}</text>
                  <text class="buyLimit address_pin" v-if="JSON.stringify(pinInfo) != '{}' && pinInfo.buyLimit > 0
                  ">{{ $L("限购数量") }}{{ pinInfo.buyLimit
                  }}{{ $L("件") }}</text>
                  <text class="buyLimit" v-if="JSON.stringify(secKillInfo) != '{}' &&
                    secKillInfo.buyLimit > 0
                  ">{{ $L("限购数量") }}{{ secKillInfo.buyLimit
                  }}{{ $L("件") }}</text>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
        <!-- 规格弹框的底部按钮 start -->

        <!-- 商品下架 start -->
        <view class="spec_btn" v-if="goodsData.state != 3">
          <button class="spec_not_stock spec_btn_only">
            {{ $L("商品已下架") }}
          </button>
        </view>
        <!-- 商品下架 end -->

        <!-- 秒杀商品start -->
        <block v-else-if="JSON.stringify(secKillInfo) != '{}' && secKillInfo.state == 2
        ">
          <!-- 秒杀已抢完 start -->
          <view class="spec_btn" v-if="defaultProduct && defaultProduct.productStock == 0">
            <button class="spec_not_stock spec_btn_only">
              {{ $L("已抢完") }}
            </button>
          </view>
          <!-- 秒杀已抢完 end -->
          <!--立即秒杀 start -->
          <view class="spec_btn" @click="buy" v-else>
            <button class="spec_seckill_btn spec_btn_only spec_seckill_btn_seck">
              {{ $L("立即秒杀") }}
            </button>
          </view>
          <!--立即秒杀 end -->
        </block>
        <!-- 秒杀商品end -->
        <!-- 预售活动 start -->
        <block v-else-if="valiInfo(preSellInfo) && preSellInfo.pre_run != 1">
          <view class="spec_btn" v-if="defaultProduct && defaultProduct.productStock == 0">
            <button class="spec_not_stock spec_btn_only">
              {{ $L("库存不足") }}
            </button>
          </view>
          <!--立即付定金/全款支付 start -->
          <view class="spec_btn" v-else @click="buy">
            <button :class="{
              spec_deposit_btn: preSellInfo.type == 1,
              spec_seckill_btn: preSellInfo.type == 2,
              spec_btn_only: true,
            }">
              {{
                preSellInfo.type == 1 ? `${$L("立即付定金")}` : $L("立即购买")
              }}
            </button>
            <!-- {{preSellInfo.type==1?`￥${preSellInfo.firstMoney}`:''}} -->
          </view>
          <!--立即付定金/全款支付 end -->
        </block>
        <!-- 预售活动 end -->
        <!-- 阶梯团活动start -->
        <block v-else-if="valiInfo(ladderInfo) && ladderInfo.ladder_run == 2">
          <view class="spec_btn" v-if="defaultProduct && defaultProduct.productStock == 0">
            <button class="spec_not_stock spec_btn_only">
              {{ $L("库存不足") }}
            </button>
          </view>
          <view class="spec_btn" v-else>
            <view @click="buy" class="specifications_btn2">
              <text>{{ $L("立即付定金") }}</text>
            </view>
          </view>
        </block>
        <!-- 阶梯团活动end -->
        <!-- 拼团活动start -->
        <block v-else-if="JSON.stringify(pinInfo) != '{}' && pinInfo.state == 1">
          <view class="spec_btn" v-if="defaultProduct && defaultProduct.productStock == 0">
            <button class="spec_not_stock spec_btn_only">
              {{ $L("库存不足") }}
            </button>
          </view>
          <block v-else>
            <view class="spec_btn" v-if="!pinButState" @click="buy('aloneBuy')">
              <button class="specifications_bottom_btn3">
                <text>{{ $L("单独买") }}</text>
                <text>({{ $L("￥") }}{{ pinInfo.productPrice }})</text>
              </button>
            </view>
            <view class="spec_btn" v-if="pinButState == 1" @click="buy">
              <button class="specifications_bottom_btn4">
                <text>{{ $L("去开团") }}</text>
                <text>{{ $L("￥")
                }}{{
                    pinInfo.leaderIsPromotion == 1
                      ? pinInfo.leaderPrice
                      : pinInfo.spellPrice
                  }}</text>
              </button>
            </view>
            <view class="spec_btn" v-if="pinButState == 2">
              <view class="specification_add" @tap="buy('aloneBuy')">
                <text>{{ $L("￥") }}{{ pinInfo.productPrice }}</text>
                <text>{{ $L("单独买") }}</text>
              </view>
              <view class="specification_buy" @tap="buy">
                <text>{{ $L("￥")
                }}{{
                    pinInfo.leaderIsPromotion == 1
                      ? pinInfo.leaderPrice
                      : pinInfo.spellPrice
                  }}</text>
                <text>{{ $L("去开团") }}</text>
              </view>
            </view>
            <view class="spec_btn" v-if="pinButState == 3" @click="buy">
              <button class="specifications_bottom_btn4">
                <text>{{ $L("去参团") }}</text>
                <text>({{ $L("￥") }}{{ pinInfo.spellPrice }})</text>
              </button>
            </view>
          </block>
        </block>
        <!-- 拼团活动end -->
        <block v-else>
          <!--库存不足 start -->
          <view class="spec_btn" v-if="defaultProduct && defaultProduct.productStock == 0">
            <button class="spec_not_stock spec_btn_only">
              {{ $L("库存不足") }}
            </button>
          </view>
          <!--库存不足 end -->
          <!-- 普通商品 start-->
          <block v-else>
            <view class="spec_btn" v-if="showSpecModelType == ''">
              <text class="spec_add_cart_btn" :class="{
                other_btn_pin: valiInfo(pinInfo) && pinInfo.state == 1,
                other_btn_lad: valiInfo(ladderInfo),
                other_btn_seck: secKillInfo && secKillInfo.state == 2,
              }" @click="addCart">{{ $L("加入购物车") }}</text>
              <text class="spec_buy_btn" :class="{
                buy_now_btn_seck: secKillInfo && secKillInfo.state == 2,
              }" @click="buy">{{ $L("立即购") }}</text>
            </view>
            <view class="spec_btn" v-if="showSpecModelType == 'add'">
              <text class="spec_add_cart_btn spec_btn_only" :class="{
                other_btn_pin: valiInfo(pinInfo) && pinInfo.state == 1,
                other_btn_lad: valiInfo(ladderInfo),
                other_btn_seck: secKillInfo && secKillInfo.state == 2,
              }" @click="addCart">{{ $L("加入购物车") }}</text>
            </view>
            <view class="spec_btn" v-if="showSpecModelType == 'buy'">
              <text class="spec_buy_btn spec_btn_only" :class="{
                spec_buy_btn_seck: secKillInfo && secKillInfo.state == 2,
              }" @click="buy">{{ $L("立即购买") }}</text>
            </view>
          </block>

          <!-- 普通商品 end-->
        </block>
      </view>
      <!-- 规格弹框的底部按钮 end -->
    </uni-popup>
    <!-- 规格弹框 end -->

    <uni-popup ref="addressModel" type="bottom" @touchmove.stop.prevent="moveHandle">
      <view class="address_list_con">
        <view class="address_top">
          <view class="back_view">
            <view></view>
          </view>
          <view class="address_top_text">{{ $L("配送至") }}</view>
          <image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" @click="addressClose"></image>
        </view>
        <scroll-view scroll-y="true" class="address_list" @touchmove.stop.prevent="moveHandle">
          <view v-for="(item, index) in addressList" :key="index" @click="checkAddress(item)" :class="{ list: true }">
            <view class="wrapper flex_row_start_center">
              <image :src="imgUrl + 'goods_detail/location.png'" v-if="sourceId != item.addressId">
              </image>
              <svgGroup type="location" width="17" height="19" :color="diyStyle_var['--color_main']">
              </svgGroup>
              <view class="flex_column_start_start">
                <view class="address-box">
                  <text :class="{
                    address: true,
                    address_on: sourceId == item.addressId,
                  }">{{ item.addressAll }}
                    {{ item.detailAddress }}
                  </text>
                </view>
              </view>
            </view>
            <view class="wrapper_right">
              <svgGroup type="checked" :color="diyStyle_var['--color_main']" width="20" height="16"
                v-if="sourceId == item.addressId">
              </svgGroup>
            </view>
          </view>
        </scroll-view>
        <view class="other_address">
          <view class="other_btn" @click="chooseArea">{{
            $L("选择其他地址")
          }}</view>
        </view>
      </view>
    </uni-popup>

    <loginPop ref="loginPop"></loginPop>

    <!-- <uni-popup ref="popLogin" type="dialog">
        <uni-popup-dialog type="input" :title="$L('提示')" :content="$L('请登录')" :duration="2000" @confirm="confirmLogin">
        </uni-popup-dialog>
      </uni-popup> -->

    <selectAddress ref="selectAddress" :sel_data="selAddressData" @selectAddress="successSelectAddress" :isBack="isBack"
      @backToAdd="backToAdd">
    </selectAddress>

    <purchasePop ref="purchasePop" :exList="exceptionProList" :exState="exState" :exStateTxt="exStateTxt"
      @goNext="goNext" :isDetail="true"></purchasePop>

    <superPop ref="superPop" :saveAmount="saveAmount"></superPop>
    <!-- #ifdef MP-WEIXIN -->
    <w-compress ref="wCompress" />
    <!-- #endif -->

    <!-- banner模块 start -->
    <!-- <productBanner></productBanner> -->
    <!-- banner模块 end -->
  </view>
</view></template>

<script>
import goodsShare from "@/components/goodsShare.vue";
import jyfParser from "@/components/jyf-parser/jyf-parser";
import purchasePop from "@/components/purchasePop.vue";
import recommendGoods from "@/components/recommend-goods.vue";
import uniPopupDialog from "@/components/uni-popup/uni-popup-dialog.vue";
import uniPopupMessage from "@/components/uni-popup/uni-popup-message.vue";
import uniPopup from "@/components/uni-popup/uni-popup.vue";
import uniRate from "@/components/uni-rate/uni-rate.vue";
import uniSwiperDot from "@/components/uni-swiper-dot/uni-swiper-dot.vue";
import selectAddress from "@/components/yixuan-selectAddress/yixuan-selectAddress";
import filters from "../../utils/filter.js";
import ladderInfo from "../components/activityDetail/ladderGroup/ladderInfo.vue";
import ladderProgress from "../components/activityDetail/ladderGroup/ladderProgress.vue";
import pinGroupList from "../components/activityDetail/pinGroup/pinGroupList.vue";
import pinInfo from "../components/activityDetail/pinGroup/pinInfo.vue";
import preInfo from "../components/activityDetail/preSale/preInfo.vue";
import superPop from "../components/superPop.vue";
import gDRank from "../rank/components/gD_rank.vue";
// #ifdef MP-WEIXIN
import WCompress from "@/components/w-compress/w-compress.vue";
// #endif
import {
  getSaveAmount,
  quillEscapeToHtml
} from "@/utils/common.js";
import { mapMutations, mapState } from "vuex";
import productBanner from "./components/productBanner.vue";
import productCarousel from './components/productCarousel.vue';
// import productRecommend from './components/productRecommend.vue';
import productSecKill from './components/productSecKill.vue';
// #ifdef H5
import Vconsole from 'vconsole'
import { weBtoa } from "../../utils/base.js";
// #endif

export default {
  components: {
    selectAddress,
    uniPopup,
    uniPopupMessage,
    uniPopupDialog,
    jyfParser,
    uniSwiperDot,
    pinInfo,
    pinGroupList,
    preInfo,
    ladderInfo,
    ladderProgress,
    uniRate,
    gDRank,
    purchasePop,
    goodsShare,
    superPop,
    recommendGoods,
    // #ifdef MP
    WCompress,
    // #endif
    productCarousel,
    productSecKill,
    // productRecommend,
    productBanner,
  },
  data() {
    return {
      filters,
      appVersion: process.env.VUE_APP_VERSION,
      lastSpreaderU: uni.getStorageSync('last_spreader_u') ?? '',
      //wx-12-start
      // #ifdef MP
      menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + "px",
      menuButtonTop: uni.getMenuButtonBoundingClientRect().top + "px",
      menuButtonleft: uni.getMenuButtonBoundingClientRect().left + "px",
      menuButtonWidth: uni.getMenuButtonBoundingClientRect().width + "px",
      menuButtonHeights:
        uni.getMenuButtonBoundingClientRect().top +
        uni.getMenuButtonBoundingClientRect().height,
      // #endif
      //wx-12-end
      imgUrl: process.env.VUE_APP_IMG_URL,
      imgurl: process.env.VUE_APP_IMG_URL,
      dialogTitle: this.$L("温馨提示!"),
      dialogCon: this.$L("您需要先登录哦～"),
      goodsData: {},
      specSelected: [],
      statistics: {}, //商品评价数据
      shareList: [],
      selAddressData: [],
      addressAll: this.$L("请选择所在地区"),
      addressList: [], //地址列表
      goodsId: "", //商品id
      curFreight: "", //当前运费
      currentSelId: "", //默认选中第一个地址
      isNavShow: false, //是否显示导航
      isSpreader: false, // 是否是推手
      isSpreaderGoods: false, // 是否是推手商品
      commission_share: 0, // 分享佣金
      commission_invite: 0, // 绑粉佣金
      navList: [
        {
          text: this.$L("商品"),
          id: 0,
        },
        {
          text: this.$L("评价"),
          id: 1,
        },
        {
          text: this.$L("推荐"),
          id: 2,
        },
        {
          text: this.$L("详情"),
          id: 3,
        },
      ],
      navListNoRecommend: [
        {
          text: this.$L("商品"),
          id: 0,
        },
        {
          text: this.$L("评价"),
          id: 1,
        },
        {
          text: this.$L("详情"),
          id: 3,
        },
      ],
      currentNav: 0, //当前点击的默认是第一项
      nav1ScrollTop: 0, //商品模块距离顶部的距离
      nav2ScrollTop: 0, //评价模块距离顶部的距离
      nav3ScrollTop: 0, //推荐模块距离顶部的距离
      nav4ScrollTop: 0, //详情模块距离顶部的距离
      codeImg: "", //海报图片
      poster: false, //生成海报
      specModel: false, //规格弹框
      currentSpecNum: 1, //当前规格弹框中的购买数量
      couponList: [], //优惠券列表
      defaultProduct: {}, //默认货品信息
      specs: [], //商品规格列表
      productId: "", //货品id
      source: "", //来源，终端类型，1、pc 2、app；3、公众号或微信内部浏览器；4、小程序
      mode: "nav", //轮播图的显示样式类型
      current: 0, //轮播图默认显示第一页
      showSpecModelType: "", //规格弹框的底部按钮的显示类型	默认：加入购物及立即购买有	add：加入购物车	buy：立即购买		nosocket库存不足	offshelf商品下架
      noOpcity: false, //顶部导航的背景是否有透明度，轮播图以上有，以下没有
      isLoading: false,
      cartNum: 0, //购物车数量
      cartNumShow: false, //购物车数量的角标是否显示
      storeInf: {}, //店铺信息
      goodsCommentsInfo: {}, //评价信息
      recommendedList: [], //店铺推荐列表
      recommendedVideoList: [], //文章推荐列表
      transparent_mask: false, //透明遮罩蒙层
      tips_show: false, //分享链接弹框
      tips: [
        {
          tips_img: process.env.VUE_APP_IMG_URL + "goods_detail/home.png",
          tips_name: this.$L("首页"),
          tips_link: "/pages/index/index",
          type: "switchTab",
        },
        {
          tips_img: process.env.VUE_APP_IMG_URL + "goods_detail/go_cart.png",
          tips_name: this.$L("购物车"),
          tips_link: "/pages/cart/cart",
          type: "switchTab",
        },
        {
          tips_img: process.env.VUE_APP_IMG_URL + "goods_detail/preson.png",
          tips_name: this.$L("个人中心"),
          tips_link: "/pages/user/user",
          type: "switchTab",
        },
      ],
      storeInf: {}, //店铺信息
      goodsParameterList: [], //规格参数列表
      openGoodsParam: false, //规格参数超出5行，点击展开，收起
      pageCurrent: 1, //优惠券列表，页
      pageSize: 10, //优惠券列表 每页的条数
      goReceiveBg: process.env.VUE_APP_IMG_URL + "coupon/coupon_pre_bg.png", //立即领取背景
      finishReceiveBg:
        process.env.VUE_APP_IMG_URL + "coupon/finishReceiveBg.png", //已抢完背景
      hasReceiveBg: process.env.VUE_APP_IMG_URL + "coupon/hasReceiveBg.png", //已领取背景
      fullDisList: [], //满优惠列表
      promotionId: "", //活动id
      secKillInfo: {}, //秒杀活动详情信息
      secKillDay: "00", //秒杀活动倒计时 天
      secKillHr: "00", //秒杀活动倒计时 时
      secKillMin: "00", //秒杀活动倒计时 分
      secKillSec: "00", //秒杀活动倒计时 秒
      preSellInfo: {}, // 预售商品详情信息
      noEdit: false, //不编辑
      ladderInfo: {}, //阶梯团信息
      ladderDay: 0, //天
      ladderHour: 0, //时
      ladderMinute: 0, //分钟
      ladderSecond: 0, //秒
      ladderProcess: "", //阶梯团进度
      pinInfo: {}, //拼团信息
      spellTeamId: 0, //拼团团队Id
      pinButState: null, //拼团按钮状态,
      showState: 0, //当当前页面进入到下一个页面时，将此值置为1，上一个页面回退到当前页面时，再onShow里判断，防止onShow触发过多
      spreaderMemberId: 0, //专门的推手分享ID设置
      goodsVideo: "",
      showControls: true, //是否显示轮播角标
      playVFlag: false,
      address_list: [],
      curAddress: "",
      sourceId: "",
      selAddressData: [],
      isBack: true,
      dis: false,
      rankData: [],
      copyname_now: false, //复制弹窗是否展示
      copyname_go: false,

      //更新价格和活动状态
      changePrice: 0,

      //下单价格提示
      exceptionProList: [],
      exState: 200,
      exStateTxt: "",

      rondomMod: false, //随机弹框
      rondomDes: {},
      clients: "",
      expressFee: 0,
      saveAmount: {},

      specRestrict: false,
      showCouponTip: false,
      allCouponsReceived: false, // 添加新的状态
      autoReceiveExecuted: false, // 添加标记，避免重复执行自动领取
      couponHasReceiveCount: 0,
      isPopularityCard: false, // 是否为人气卡商品
      currentSpecImageIndex: 0, // 当前选中规格的索引
    };
  },
  computed: {
    ...mapState(["hasLogin", "userInfo", "userCenterData", "memberConfig"]),
    activityState() {
      let { secKillInfo, ladderInfo, preSellInfo, pinInfo } = this;
      return (
        JSON.stringify(secKillInfo) != "{}" ||
        JSON.stringify(ladderInfo) != "{}" ||
        JSON.stringify(preSellInfo) != "{}" ||
        JSON.stringify(pinInfo) != "{}"
      );
    },
    currentMemberU(){
      const memberId=this.userInfo?.memberId;
      if(memberId){
        return weBtoa(memberId);
      }
      return '';
    },
    actiState() {
      let now = new Date().getTime();
      switch (this.defaultProduct.promotionType) {
        case 104: {
          return this.valiInfo(this.secKillInfo) && this.secKillInfo.state == 2;
        }
        case 103: {
          let st = new Date(this.preSellInfo.startTime).getTime();
          let et = new Date(this.preSellInfo.endTime).getTime();
          return this.valiInfo(this.preSellInfo) && now > st && et > now;
        }
        case 105: {
          let st = new Date(this.ladderInfo.startTime).getTime();
          let et = new Date(this.ladderInfo.endTime).getTime();
          return this.valiInfo(this.ladderInfo) && now > st && et > now;
        }
        case 102: {
          let st = new Date(this.pinInfo.startTime).getTime();
          let et = new Date(this.pinInfo.endTime).getTime();
          return this.valiInfo(this.pinInfo) && now > st && et > now;
        }
      }
    },

    getDisplayText() {
      if (this.isPopularityCard) {
        // 人气卡商品
        if (
          this.allCouponsReceived ||
          this.couponList.every(
            (coupon) => coupon.isReceive === 2 || coupon.isReceive === 3
          )
        ) {
          return this.$L("人气卡优惠券已领取");
        }
      }
      // 非人气卡商品或未领取完
      return this.couponHasReceiveCount > 0
        ? this.$L(`${this.couponHasReceiveCount}张优惠券可用`)
        : this.$L("无可用优惠券");
    },
  },
  // 监听返回事件

  async onLoad(options) {

    console.log('商品详情页1️⃣ onLoad');






    this.client();
    uni.showLoading({
      title: this.$L("加载中！"),
    });
    this.showState = 0;

    if (this.$Route.query.scene) {
      let url = decodeURIComponent(decodeURIComponent(this.$Route.query.scene));
      let url_param_array = url.split(",");
      this.productId = url_param_array[0];
      this.goodsId = url_param_array[1];
      this.spreaderMemberId = url_param_array[2] ? url_param_array[2] : 0;
    } else {
      this.productId = this.$Route.query.productId;
      this.goodsId = this.$Route.query.goodsId;
      if (this.$Route.query.u != undefined && this.$Route.query.u != "") {
        this.spreaderMemberId = decodeURIComponent(this.$Route.query.u);
      }
    }
    await this.getGoodsDetail(this.productId);

    this.addLook(this.productId);
    //wx-1-start
    //#ifdef MP-WEIXIN
    this.source = 4;
    //#endif
    //wx-1-end

    if (this.$Route.query.videoId) {
      //更新视频商品点击量
      this.updateVideoClick(this.$Route.query.videoId, this.goodsId);
    }
    uni.getSystemInfo({
      success: (res) => {
        let clientHeight = res.windowHeight,
          clientWidth = res.windowWidth,
          rpxR = 750 / clientWidth;
        let calc = clientHeight * rpxR;
        this.winHeight = calc;
      },
    });
    // #ifdef H5
    this.isWeiXinBrower = this.$isWeiXinBrower();
    // #endif
    this.goTop(); //一键回到页面顶部



    // #ifdef H5
    window.addEventListener(
      "mousewheel",
      () => {
        this.dis = false;
      },
      false
    );
    // #endif
    //进入商品详情，无论下单与否都先清掉上次选择的发票信息
    this.clearInvoiceStorage();
    this.specIdMap = new Map();

    // 延迟100ms后执行自动领取
    setTimeout(() => {
      if (
        this.hasLogin &&
        this.couponList?.length &&
        this.goodsData?.categoryId3 === 286461
      ) {
        this.autoReceiveCoupons();
      }
    }, 100);
  },
  async onShow() {
    console.log('商品详情页2️⃣ onShow');
    console.log('[onShow]:show=', this.showState, 'product=', this.productId, 'login=', this.hasLogin)

    await this.$handleFx()

    // 调用推手相关接口
    if (this.hasLogin) {

      try {
        await this.getSpreaderGoodsDetail();
        await this.getSpreaderInfo();
        console.log('推手接口调用完成, isSpreader:', this.isSpreader);


        // 更新分销参数
      } catch (err) {
        console.error('推手接口调用失败:', err);
      }
    } else {
      console.log('用户未登录,跳过推手接口调用');
    }

    // #ifdef H5
    this.$request({
      url: 'v3/system/front/setting/getSettings',
      method: 'GET',
      data: {
        names: 'test_member_ids'
      }
    }).then(res => {
      if (res.state === 200 && res.data) {
        console.log('[onShow]:test_member_ids=', res.data)
        if (!res.data || res.data.length === 0) {
          return;
        }
        const testMemberIds = res.data[0].split(',');
        if (this.userCenterData.memberId && testMemberIds.includes(this.userCenterData.memberId.toString())) {
          // Dynamically import vConsole
          new Vconsole(
            {
              theme: 'dark', // 主题：'light' | 'dark'
              target: document.body,
              defaultPlugins: ['system', 'network', 'element', 'storage'],
              position: {
                left: 0,
                bottom: 10
              }
            }
          );
          console.log('[debugEnable]', this.userCenterData.memberId, testMemberIds)
        }
      }
    }).catch(err => {
      console.error('Error fetching test member IDs:', err);
    });
    // #endif

    if (this.showState == 1 && this.productId) {


      this.getGoodsDetail(this.productId).then(() => {
        if (
          this.hasLogin &&
          this.couponList?.length &&
          this.goodsData?.categoryId3 === 286461
        ) {
          this.autoReceiveCoupons();
        }
      });
    }
  },
  // #ifdef MP-WEIXIN
  onShareAppMessage: async function () {
    if (!this.imageUrl) {
      this.imageUrl = await this.compressImage().catch(console.log);
    }
    return {
      title: this.goodsData.goodsName,
      path: this.$getPath(),
      imageUrl: this.imageUrl.tempFilePath,
    };
  },

  onShareTimeline: async function () {
    if (!this.imageUrl) {
      this.imageUrl = await this.compressImage().catch(console.log);
    }
    return {
      title: this.goodsData.goodsName,
      query: this.$getPath(),
      imageUrl: this.imageUrl.tempFilePath,
    };
  },
  // #endif

  //页面滚动事件
  onPageScroll(res) {
    //监听滚动事件，获取滚动条的当前位置
    this.tips_show = false;
    this.transparent_mask = false;
    if (this.dis) {
      return;
    }

    if (res.scrollTop > 50) {
      this.isNavShow = true;
      this.noOpcity = Boolean(res.scrollTop > 350);
      if (res.scrollTop < this.nav2ScrollTop) {
        this.currentNav = 0;
      } else {
        if (this.recommendedList && this.recommendedList.length > 0) {
          //有推荐
          if (
            res.scrollTop >= this.nav2ScrollTop &&
            res.scrollTop < this.nav3ScrollTop
          ) {
            this.currentNav = 1;
          } else if (
            res.scrollTop >= this.nav3ScrollTop &&
            res.scrollTop < this.nav4ScrollTop
          ) {
            this.currentNav = 2;
          } else if (res.scrollTop >= this.nav4ScrollTop) {
            this.currentNav = 3;
          }
        } else {
          //无推荐
          if (
            res.scrollTop >= this.nav2ScrollTop &&
            res.scrollTop < this.nav4ScrollTop
          ) {
            this.currentNav = 1;
          } else if (res.scrollTop >= this.nav4ScrollTop) {
            this.currentNav = 2;
          }
        }
      }
    } else {
      this.isNavShow = false;
    }
  },

  onReachBottom() {
    this.$refs.recommendGoods?.getMoreData();
  },

  methods: {
    ...mapMutations(["saveChatBaseInfo"]),

    // 获取推手商品详情
    getSpreaderGoodsDetail() {
      this.$request({
        url: "v3/spreader/front/spreaderGoods/detail",
        method: "GET",
        data: {
          goodsId: this.goodsId,
          productId: this.productId
        }
      }).then(res => {
        console.log("推手商品详情:", res);
        // 判断是否为推手商品
        this.isSpreaderGoods = res.state === 200 && res.data;
        if (res.state === 200 && res.data) {
          // 计算分享佣金和绑粉佣金
          this.commission_share = res.data.commission_share || res.data.commission * 0.5;
          this.commission_invite = res.data.commission_invite || res.data.commission * 0.5;
        }
      });
    },

    // 更新SKU变化时的佣金信息
    updateCommissionForSku() {
      this.$request({
        url: "v3/spreader/front/spreaderGoods/detail",
        method: "GET",
        data: {
          goodsId: this.goodsId,
          productId: this.productId
        }
      }).then(res => {
        console.log("SKU变化后的推手商品详情:", res);
        if (res.state === 200 && res.data) {
          // 更新分享佣金和绑粉佣金
          this.commission_share = res.data.commission_share || res.data.commission * 0.5;
          this.commission_invite = res.data.commission_invite || res.data.commission * 0.5;
        }
      });
    },

    // 获取推手信息
    getSpreaderInfo() {
      console.log('调用推手信息接口, 登录状态:', this.hasLogin);
      this.$request({
        url: "v3/spreader/front/spreader/getInfo",
        method: "GET"
      }).then(res => {
        console.log("推手信息:", res);
        // 判断是否为推手
        this.isSpreader = res.state === 200 && res.data && res.data.isSpreader;
      }).catch(err => {
        console.error('获取推手信息失败:', err);
        // 接口失败时默认不显示推手信息
        this.isSpreader = false;
      });
    },

    compressImage() {

      // #ifdef MP-WEIXIN
      return this.$refs.wCompress.start(this.defaultProduct.goodsPics[0], {
        pixels: 4000000, // 最大分辨率，默认二百万
        quality: 0.9, // 压缩质量，默认0.8
        base64: false, // 是否返回base64，默认false，非H5有效
        wxShareCropped: true
      })
      // #endif
    },

    clearInvoiceStorage() {
      uni.removeStorage({
        key: "invoice_info",
      });
      uni.removeStorage({
        key: "is_need_invoice",
      });
    },
    superPopOpen() {
      this.$refs.superPop.open();
    },
    client() {
      let client;
      // #ifdef H5
      this.clients = "h5";
      // #endif
      //wx-13-start
      // #ifdef MP
      this.clients = "mp";
      // #endif
      //wx-13-end
      //app-2-start

      //app-2-end
    },
    //更新视频商品点击量
    updateVideoClick(videoId, goodsId) {
      let param = {};
      param.data = {};
      param.url = "v3/video/front/video/updateGoodsClickNum";
      param.method = "POST";
      param.data.videoId = videoId;
      param.data.goodsId = goodsId;
      this.$request(param).then((res) => {
        if (res.state == 200) {
        }
      });
    },

    //进入详情时请求足迹接口，加入足迹
    addLook(productId) {
      this.$request({
        url: "v3/member/front/productLookLog/add",
        data: {
          productId: this.productId, //货品id
        },
        method: "POST",
      }).then((res) => {
        if (res.state == 200) {
        } else {
          this.$api.msg(res.msg);
        }
      });
    },



    successSelectAddress(address) {
      //选择成功回调
      this.curAddress = "";
      address.map((item) => {
        this.curAddress += item.name;
      });
      this.cityCode = address[address.length - 2].code;
      this.getUserEx(this.cityCode);
      this.getUserSaveAmount();
    },

    //获取页面元素模块距离顶部的距离
    getSelectorQuery() {
      let query = uni.createSelectorQuery().in(this);
      // 获取状态栏的高度
      let statusBarHeight = 0;
      //app-3-start

      //app-3-end
      //获取对应模块到顶部的距离
      query
        .select("#nav1")
        .boundingClientRect((res) => {
          if (res) {
            this.nav1ScrollTop = res.top - (80 + statusBarHeight);
          }
        })
        .exec();
      query
        .select("#nav2")
        .boundingClientRect((res) => {
          if (res) {
            this.nav2ScrollTop = res.top - (80 + statusBarHeight);
          }
        })
        .exec();
      this.$nextTick(function () {
        if (this.recommendedList && this.recommendedList.length > 0) {
          //有店铺推荐模块
          query
            .select("#nav3")
            .boundingClientRect((res) => {
              if (res) {
                this.nav3ScrollTop = res.top - (80 + statusBarHeight);
              }
            })
            .exec();
        }
      });
      query
        .select("#nav4")
        .boundingClientRect((res) => {
          if (res) {
            this.nav4ScrollTop = res.top - (80 + statusBarHeight);
          }
        })
        .exec();
    },
    //点击导航
    clickNav(navId) {
      this.currentNav = navId;
      this.dis = true;
      if (navId == 0) {
        this.isNavShow = true;
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 300,
        });
        this.isNavShow = false;
      } else if (navId == 1) {
        uni.pageScrollTo({
          scrollTop: this.nav2ScrollTop,
          duration: 300,
        });
      } else if (navId == 2) {
        uni.pageScrollTo({
          scrollTop: this.nav3ScrollTop,
          duration: 300,
        });
      } else if (navId == 3) {
        uni.pageScrollTo({
          scrollTop: this.nav4ScrollTop,
          duration: 300,
        });
      }
    },

    goBack() {
      console.log('web返回上一页:goBack')

      //有返回的页面则直接返回，uni.navigateBack默认返回失败之后会自动刷新页面，无法继续返回
      const pagesLen = getCurrentPages().length
      if (pagesLen > 1) {
        console.log('web返回上一页:navigateBack', pagesLen)
        uni.navigateBack(1)
        return
      }
      //vue router 可以返回uni.navigateBack失败的页面，但是会重新加载
      //router.go失败之后则重定向首页
      // #ifndef MP
      let a = this.$router.go(-1)
      console.log('返回上一页:$router.go(-1)', a)
      if (a == undefined) {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
      // #endif
      this.$Router.back(1)
    },
    goSpreaderCenter() {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin()
      } else {
        this.showState = true
        this.$Router.push('/extra/tshou/user/user')
      }
    },

    //轮播图切换
    change(e) {
      this.current = e.detail.current;
      if (this.goodsVideo && e.detail.current == 0) {
        this.showControls = false;
      } else {
        this.showControls = true;
      }
      //wx-14-start
      // #ifdef MP
      if (this.goodsVideo && this.current && this.playVFlag) {
        this.playVFlag = false;
      } else if (this.goodsVideo && e.detail.current == 0) {
        this.playVFlag = true;
      }
      // #endif
      //wx-14-end
    },
    //跳转客服页面
    toKefu() {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin();
        return;
      }
      let chatBaseInfo = {};
      chatBaseInfo.memberId = this.userCenterData.memberId;
      chatBaseInfo.memberName = this.userCenterData.memberName;
      chatBaseInfo.memberNickName = this.userCenterData.memberNickName;
      chatBaseInfo.memberAvatar = this.userCenterData.memberAvatar;
      chatBaseInfo.storeId = this.goodsData.storeInf.storeId;
      chatBaseInfo.storeLogo = this.goodsData.storeInf.storeLogo;
      chatBaseInfo.storeName = this.goodsData.storeInf.storeName;
      chatBaseInfo.source = "goods";
      chatBaseInfo.showData = {
        productId: this.goodsData.defaultProduct.productId,
        goodsName: this.goodsData.goodsName,
        goodsImage: this.goodsData.shareImage,
        goodsPrice: this.goodsData.defaultProduct.productPrice,
        goodsId: this.goodsData.goodsId,
      };
      this.saveChatBaseInfo(chatBaseInfo);
      this.$Router.push({
        path: "/standard/chat/detail",
        query: {
          vid: this.goodsData.storeInf.storeId,
        },
      });
    },

    backToAdd() {
      this.$refs.selectAddress.hidden();
      this.$refs.addressModel.open();
    },

    showAddress() {
      if (!this.hasLogin || this.addressList.length == 0) {
        this.$refs.selectAddress.showNoMask();
        this.isBack = false;
      } else {
        this.$refs.addressModel.open();
        this.isBack = true;
      }
    },

    chooseArea() {
      this.$refs.addressModel.close();
      this.$refs.selectAddress.showNoMask();
    },

    // 点击播放按钮,或者视频播放结束隐藏视频组件
    playVideo(type) {
      if (type == "on") {
        this.playVFlag = true;
      } else if (type == "end") {
        this.playVFlag = false;
      }
    },

    //去分享
    goShare() {
      // 分享登录校验 @xuhe
      if(!this.hasLogin){
        this.$refs.loginPop.openLogin();
        return;
      }

      let {
        goodsData,
        showPrice,
        defaultProduct: { productPrice, marketPrice },
      } = this;
      let _marketPrice = 0;
      let _productPrice = 0;
      if (!Object.is(Number(showPrice), Number(productPrice))) {
        _productPrice = showPrice;
        _marketPrice = productPrice;
      } else {
        _productPrice = productPrice;
        _marketPrice = marketPrice;
      }

      let info = {
        ...goodsData,
      };
      info.defaultProduct.productPrice = _productPrice;
      info.defaultProduct.marketPrice = _marketPrice;
      info.shareLink = this.$getPath();
      console.log("去分享", info);
      this.$refs.goodsShare.injectInfo(info);
    },

    //回到页面顶部
    goTop() {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300,
      });
      this.currentNav = 0;
    },
    //获取商品详情信息
    getGoodsDetail(productId) {
      console.log('当前页面的 productId:', productId); // 打印当前页面的 productId
      if (!productId) {
        return
      }

      productId = parseInt(productId)

      this.$request({
        url: "v3/goods/front/goods/details",
        data: {
          productId: productId, //货品id
          goodsId: this.goodsId, //商品id
          source: this.source,
        },
      })
        .then((res) => {
          if (res.state == 200) {
            // 先设置基础数据，避免undefined错误
            this.goodsData = res.data; //详情信息
            this.goodsId = res.data.goodsId;

            // 判断返回的默认货品ID是否与当前的不一致
            if (res.data.defaultProduct && res.data.defaultProduct.productId !== productId) {
              console.log('接口返回的新 productId:', res.data.defaultProduct.productId, productId, typeof (res.data.defaultProduct.productId), typeof (productId)); // 打印接口返回的新 productId
              console.log('检测到 productId 不一致，即将重定向...'); // 打印重定向提示

              // 重定向到最新的商品详情页
              this.$Router.replace({
                path: "/standard/product/detail",
                query: {
                  productId: res.data.defaultProduct.productId,
                  goodsId: res.data.goodsId
                }
              });
              return;
            }

            if (res.data.topTemplateContent) {
              res.data.topTemplateContent = quillEscapeToHtml(
                res.data.topTemplateContent
              );
            }
            if (res.data.goodsDetails) {
              res.data.goodsDetails = quillEscapeToHtml(res.data.goodsDetails);
              this.replaceRichStyle(res);
            }
            if (res.data.bottomTemplateContent) {
              res.data.bottomTemplateContent = quillEscapeToHtml(
                res.data.bottomTemplateContent
              );
            }
            this.goodsVideo = res.data.goodsVideo;
            this.showControls = this.goodsVideo ? false : true;
            this.goodsParameterList = [];
            if (this.goodsData.brandName) {
              this.goodsParameterList.push({
                parameterName: this.$L("品牌"),
                parameterValue: this.goodsData.brandName,
              });
            }
            this.goodsParameterList = this.goodsParameterList.concat(
              res.data.goodsParameterList
            ); //规格参数列表

            // #ifdef H5
            document.title = this.goodsData.goodsName;
            // #endif
            //wx-2-start
            // #ifdef MP-WEIXIN
            uni.setNavigationBarTitle({
              title: this.goodsData.goodsName,
            });
            // #endif
            //wx-2-end

            // 判断是否为人气卡
            this.isPopularityCard = res.data.categoryId3 === 286461;
            console.log("是否为人气卡商品:", this.isPopularityCard);
          } else {
            //错误提示
            this.$api.msg(res.msg);
            this.$Router.back(1);
          }
        })
        .then(() => {
          this.getGoodsDetailDynamic();
          // this.getRecommend(); //获取店铺推荐商品列表
          // this.getRecommendVideo();
          // this.getGoodsComment(); //评价信息
          this.getCouponList(); //获取优惠券列表
          this.fullDiscountList(); //获取满优惠列表
          this.getCartNum(); //获取购物车数据
          this.hasLogin ? this.getAddressList() : this.getUserEx();
          this.getRank();
          this.getUserSaveAmount();
        });
    },

    recommendGoodsLoaded() {
      if (!this.goodsData || !this.goodsData.storeInf) return;
      let statParam = {
        behaviorType: "gpv",
        goodsId: this.goodsId,
        storeId: this.goodsData.storeInf.storeId,
      };
      let {
        query: { fromRecommend },
      } = this.$Route;
      if (
        fromRecommend &&
        this.$refs.recommendGoods?.isWidthinFrom(fromRecommend)
      ) {
        statParam.clickLink = fromRecommend;
      }
      this.$sldStatEvent(statParam);
    },

    replaceRichStyle(res) {
      res.data.goodsDetails = res.data.goodsDetails.replace(/px/g, "rpx");
      res.data.goodsDetails = res.data.goodsDetails.replace(
        ".ssd-module-wrap{",
        ".ssd-module-wrap{line-height:0;"
      );
      res.data.goodsDetails = res.data.goodsDetails.replace(
        /\n        \n/g,
        ""
      );
      res.data.goodsDetails = res.data.goodsDetails.replace(/\n/g, "");
      res.data.goodsDetails = res.data.goodsDetails.replace(
        "background-repeat:no-repeat;",
        ""
      );
      res.data.goodsDetails = res.data.goodsDetails.replace(
        ".ssd-module-wrap{line-height:0",
        ".ssd-module-wrap .ssd-module{margin-top:-1px}.ssd-module-wrap{line-height:0"
      );
      //wx-3-start
      // #ifdef MP-WEIXIN
      let imgCssOrigin = res.data.goodsDetails.match(/<style>.*?\<\/style>/g);
      let newCombineGoodsDetail;
      if (imgCssOrigin) {
        let imgCss = imgCssOrigin[0];
        imgCss = imgCss.replace(/<style>/g, "");
        imgCss = imgCss.replace(/<\/style>/g, "");
        imgCss = imgCss.replace(
          /.ssd-module-wrap .ssd-module{.*?\.ssd-hide{display:none}/g,
          ""
        );
        newCombineGoodsDetail = res.data.goodsDetails.replace(
          /<style>.*?\<\/style>/g,
          ""
        );
        let imgArray = imgCss.split(".ssd-module-wrap ");
        if (imgArray.length != undefined && imgArray.length) {
          imgArray.map((item) => {
            if (item) {
              let tempClass = item.match(/..*?\{/g)[0];
              tempClass = tempClass.slice(1, -1);
              let tempStyle = item.match(/{.*?\}/g)[0];
              tempStyle = tempStyle.slice(1, -1);
              item = item.replace(/<style>/g, "");
              let replace1 = `class="ssd-module ${tempClass}`;
              let replace2 = `style=\'${tempStyle}\' class="ssd-module ${tempClass}`;
              newCombineGoodsDetail = newCombineGoodsDetail.replace(
                replace1,
                replace2
              );
            }
          });
        }
      }
      if (newCombineGoodsDetail) res.data.goodsDetails = newCombineGoodsDetail;
      // #endif
      //wx-3-end
    },

    getGoodsDetailDynamic() {

      this.$request({
        url: "v3/goods/front/goods/details2",
        data: {
          productId: this.productId, //货品id
          goodsId: this.goodsId, //商品id
          source: this.source,
        },
      })
        .then(async (res) => {
          if (res.state == 200) {
            let dynamicData = [
              "effectSpecValueIds",
              "followGoods",
              "sales",
              "state",
              "shareLink",
              "shareImage",
              "isVirtualGoods",
              "storeInf",
              "deliveryMethod",
            ];
            dynamicData.forEach((item) => {
              this.goodsData[item] = res.data[item];
            });
            this.defaultProduct = res.data.defaultProduct; //默认货品信息
            this.changePrice = this.defaultProduct.productPrice;
            this.showPrice =
              this.defaultProduct.superPrice ||
              this.defaultProduct.productPrice;
            this.specs = res.data.specs; //规格列表
            this.specs.forEach((item) => {
              let value = item.specValueList.find((i) => i.checkState == 1);
              this.specIdMap.set(item.specId, value.specValueId);
            });

            this.storeInf = res.data.storeInf; //店铺信息
            this.promotionId = this.defaultProduct.promotionId; //活动id

            if (this.promotionId && this.defaultProduct.promotionType == 104) {
              // 秒杀
              await this.getSecKill();
            } else if (this.defaultProduct.promotionType == 103) {
              // 预售
              await this.getPreSell();
            } else if (
              this.promotionId &&
              this.defaultProduct.promotionType == 102
            ) {
              //拼团
              await this.getPinInfo();
            } else if (
              this.promotionId &&
              this.defaultProduct.promotionType == 105
            ) {
              await this.getLadder();
            } else {
              this.pinInfo = {};
              this.secKillInfo = {};
              this.preSellInfo = {};
              this.ladderInfo = {};
            }
            this.isLoading = true;
            uni.hideLoading();

            let shareData = {
              title: this.goodsData.goodsName,
              desc: this.goodsData.goodsBrief,
              link: this.$getPath(),
              imgUrl: this.goodsData.shareImage,
            };
            // #ifdef H5
            console.log("h5分享卡片", shareData)

            this.$WXBrowserShareThen(1, shareData);
            // this.$WXBrowserShareThen(2, shareData);
            // #endif
          } else {
            this.$api.msg(res.msg);
          }
        })
        .then(() => {
          this.getSelectorQuery();
        });
    },

    //获取预售商品详情
    async getPreSell() {
      let param = {};
      param.url = "v3/promotion/front/preSell/detail";
      param.method = "GET";
      param.data = {};
      param.data.productId = this.productId;
      param.data.promotionId = this.promotionId;
      await this.$request(param).then((res) => {
        if (res.state == 200) {
          let now = new Date();
          let preStartDate = new Date(res.data.startTime.replace(/-/g, "/"));
          let preEndDate = new Date(res.data.endTime.replace(/-/g, "/"));
          let result = res.data;
          this.preSellInfo = result;
          this.productId = this.preSellInfo.productId;
          this.showPrice = this.preSellInfo.presellPrice;
          this.changePrice =
            this.preSellInfo.type == 1
              ? this.preSellInfo.firstMoney
              : this.preSellInfo.presellPrice;
          this.preSellInfo.endTime = res.data.endTime.substring(
            0,
            this.preSellInfo.endTime.length - 3
          );
          this.preSellInfo.startTime = res.data.startTime.substring(
            0,
            this.preSellInfo.startTime.length - 3
          );
          if (now > preStartDate && now < preEndDate) {
            this.preSellInfo.pre_run = 2; //活动进行中
          } else if (now < preStartDate) {
            this.preSellInfo.pre_run = 1; //活动未开始
          } else if (now > preEndDate) {
            this.preSellInfo.pre_run = 3; //活动已结束
          }
        } else {
          this.$api.msg(res.msg);
        }
      });
    },

    //获取拼团商品信息
    async getPinInfo() {
      if (this.secInterval) {
        clearInterval(this.secInterval);
      }
      let param = {};
      param.url = "v3/promotion/front/spell/detail";
      param.method = "GET";
      param.data = {};
      param.data.productId = this.productId;
      param.data.promotionId = this.promotionId;
      await this.$request(param).then((res) => {
        if (res.state == 200) {
          let result = res.data;
          this.pinInfo = result;
          this.changePrice = this.pinInfo.spellPrice;
          this.showPrice = this.pinInfo.spellPrice;
          if (this.showState == 1) {
            this.$refs.pinGroup.getPinTeam();
          }
        } else {
          this.$api.msg(res.msg);
        }
      });
    },

    handleJoinGroup(e) {
      if (e.pinState == 1) {
        this.spellTeamId = e.spellTeamId;
      } else if (e.pinState == 2) {
      } else if (e.pinState == 3) {
        this.showSpecModel("joinLeague");
      }
    },

    getRank() {
      this.$request({
        url: "v3/goods/front/goods/rankList",
        data: {
          goodsId: this.goodsId,
        },
      }).then((res) => {
        if (res.state == 200) {
          this.rankData = res.data;
        }
      });
    },

    //获取秒杀商品详情
    async getSecKill() {
      if (this.secInterval) {
        clearInterval(this.secInterval);
      }
      let param = {};
      param.url = "v3/promotion/front/seckill/detail";
      param.method = "GET";
      param.data = {};
      param.data.productId = this.productId;
      param.data.promotionId = this.promotionId;
      await this.$request(param).then((res) => {
        if (res.state == 200) {
          let result = res.data;
          this.secKillInfo = result;
          this.productId = this.secKillInfo.productId;

          if (this.secKillInfo.state == 2) {
            this.changePrice = this.secKillInfo.seckillPrice;
            this.showPrice = this.secKillInfo.seckillPrice;
          }
          if (this.secKillInfo.state == 1 || this.secKillInfo.state == 2) {
            let countTime = 0;
            countTime = this.secKillInfo.distanceEndTime; //剩余时间	秒
            this.secInterval = setInterval(() => {
              if (countTime == 0) {
                //倒计时结束，清除倒计时
                clearInterval(this.secInterval);
                this.getGoodsDetail(this.secKillInfo.productId);
              } else {
                countTime--;
                let day = parseInt(countTime / 60 / 60 / 24);
                let hours = parseInt((countTime / 60 / 60) % 24);
                let minutes = parseInt((countTime / 60) % 60);
                let seconds = parseInt(countTime % 60);
                this.secKillDay = day;
                this.secKillHr = hours > 9 ? hours : "0" + hours;
                this.secKillMin = minutes > 9 ? minutes : "0" + minutes;
                this.secKillSec = seconds > 9 ? seconds : "0" + seconds;
              }
            }, 1000);
          }
        } else {
          this.$api.msg(res.msg);
        }
      });
    },

    // 阶梯团商品详情
    async getLadder() {
      if (this.secInterval) {
        clearInterval(this.secInterval);
      }
      let param = {};
      param.url = "v3/promotion/front/ladder/group/detail";
      param.method = "GET";
      param.data = {};
      param.data.productId = this.productId;
      param.data.promotionId = this.promotionId;
      await this.$request(param)
        .then((res) => {
          if (res.state == 200) {
            let result = res.data;
            this.ladderInfo = result;
            this.productId = this.ladderInfo.productId;
            this.changePrice = this.ladderInfo.advanceDeposit;
            this.showPrice = this.ladderInfo.currentPrice;
            this.ladderProcess =
              result.joinedNum <
                result.ruleList[result.ruleList.length - 1].joinGroupNum
                ? (result.joinedNum /
                  result.ruleList[result.ruleList.length - 1].joinGroupNum) *
                100
                : 100;
            let now = new Date();
            let start = new Date(this.ladderInfo.startTime.replace(/-/g, "/"));
            let end = new Date(this.ladderInfo.endTime.replace(/-/g, "/"));
            if (now < start) {
              this.ladderInfo.ladder_run = 1;
            } else if (now > start && now < end) {
              this.ladderInfo.ladder_run = 2;
            } else {
              this.ladderInfo.ladder_run = 3;
            }
          } else {
            this.$api.msg(res.msg);
          }
        })
        .catch((e) => {
          //异常处理
        });
    },

    //秒杀活提醒我及取消提醒
    secKillPreview() {
      if (!this.hasLogin) {
        //提示登陆
        this.$refs.popLogin.open();
        return;
      } else {
        let param = {};
        param.url = "v3/promotion/front/seckill/isRemind";
        param.method = "POST";
        param.data = {};
        param.data.key = this.userInfo.member_access_token;
        param.data.stageProductId = this.secKillInfo.stageProductId; //秒杀商品id
        this.$request(param)
          .then((res) => {
            if (res.state == 200) {
              this.$api.msg(res.msg);
              this.secKillInfo.isRemind = !this.secKillInfo.isRemind;
            } else {
              this.$api.msg(res.msg);
            }
          })
          .catch((e) => {
            //异常处理
          });
      }
    },
    /**选择规格值
     * @param {Object} type 类型   值：choice,规格选择    default:默认
     * @param {Object} specId 父级规格值
     * @param {Object} specValueId   点击的当前的规格值
     */
    selectSpecVal(type, specId, specValueId) {
      let that = this;

      // 在发送请求前就更新图片
      console.log('选择规格:', specId, specValueId);
      const selectedSpec = this.specs.find(spec => spec.specId === specId);
      if (selectedSpec) {
        console.log('找到规格:', selectedSpec);
        const selectedValue = selectedSpec.specValueList.find(val => val.specValueId === specValueId);
        if (selectedValue) {
          console.log('找到规格值:', selectedValue);

          // 获取规格值在列表中的索引
          const valueIndex = selectedSpec.specValueList.findIndex(val => val.specValueId === specValueId);
          console.log('规格值索引:', valueIndex);

          // 如果规格值有图片就使用规格图片,否则使用对应索引的商品图片
          if (selectedValue.image) {
            console.log('使用规格图片:', selectedValue.image);
            const imageIndex = this.defaultProduct.goodsPics.findIndex(pic => {
              return pic.includes(selectedValue.image) || selectedValue.image.includes(pic);
            });
            if (imageIndex !== -1) {
              this.currentSpecImageIndex = imageIndex;
            }
          } else if (this.defaultProduct.goodsPics && this.defaultProduct.goodsPics.length > valueIndex) {
            console.log('使用商品图片索引:', valueIndex);
            this.currentSpecImageIndex = valueIndex;
          }
        }
      }

      if (this.specIdMap.get(specId) == specValueId) {
        this.specIdMap.delete(specId);
      } else {
        this.specIdMap.set(specId, specValueId);
      }
      let choiceSpecIds = Array.from(this.specIdMap.values());

      let param = {};
      param.url = "v3/goods/front/goods/productInfo";
      param.method = "GET";
      param.data = {};
      param.data.goodsId = that.goodsId;
      if (choiceSpecIds.length) {
        param.data.specValueIds = choiceSpecIds.join(",");
      }
      that.$request(param).then((res) => {
        if (res.state == 200) {
          let result = res.data;
          if (result.defaultProduct) {
            this.defaultProduct = result.defaultProduct;
            this.productId = result.defaultProduct.productId;
            this.changePrice = this.defaultProduct.productPrice;
            this.showPrice = this.defaultProduct.productPrice;
            this.promotionId = this.defaultProduct.promotionId; //活动id
            this.defaultProduct.promotionType =
              result.defaultProduct.promotionType;
            this.currentSpecNum = 1;

            if (!this.defaultProduct.promotionType) {
              this.preSellInfo = {};
              this.ladderInfo = {};
              this.secKillInfo = {};
              this.pinInfo = {};
            } else {
              if (
                this.promotionId &&
                this.defaultProduct.promotionType == 104
              ) {
                // 秒杀
                this.getSecKill();
              } else if (this.defaultProduct.promotionType == 103) {
                // 预售
                this.getPreSell();
              } else {
                this.secKillInfo = {};
                this.preSellInfo = {};
              }
              if (
                this.promotionId &&
                this.defaultProduct.promotionType == 102
              ) {
                //拼团
                this.getPinInfo();
              } else {
                this.pinInfo = {};
              }
              if (
                this.promotionId &&
                this.defaultProduct.promotionType == 105
              ) {
                this.getLadder();
              } else {
                this.ladderInfo = {};
              }
            }
            this.getUserEx(this.cityCode);
            this.specRestrict = false;
          } else {
            this.specRestrict = true;
          }

          this.specs = result.specs; //规格列表
          this.specs.forEach((item) => {
            let value = item.specValueList.find((i) => i.checkState == 1);
            if (value) {
              this.specIdMap.set(item.specId, value.specValueId);
            } else if (this.specIdMap.get(item.specId)) {
              this.specIdMap.delete(item.specId);
            }
          });
          if (result.shareLink) {
            this.goodsData.shareLink = result.shareLink;
          }
          this.current = 0;
          
          // 更新佣金信息当SKU改变时
          if (this.hasLogin && this.isSpreader) {
            this.updateCommissionForSku();
          }
        } else {
          this.$api.msg(res.msg);
        }
      });
    },
    //获取商品评价
    getGoodsComment() {
      let param = {};
      param.url = "v3/goods/front/goods/comment";
      param.method = "GET";
      param.data = {};
      param.data.productId = this.productId;
      this.$request(param).then((res) => {
        if (res.state == 200) {
          let result = res.data;
          this.goodsCommentsInfo = result;
        } else {
          this.$api.msg(res.msg);
        }
      });
    },

    //加入购物车功能
    addCart() {
      if (!this.hasLogin && this.goodsData.shopType == 2) {
        this.$refs.popLogin.open();
        return;
      }
      const that = this;
      if (!this.hasLogin) {
        let specValues = ""; // 存储商品多规格值
        if (this.specs && this.specs.length > 0) {
          this.specs.forEach((item) => {
            item.specValueList.forEach((items) => {
              if (items.checkState == 1) {
                specValues += items.specValue + " ";
              }
            });
          });
        }
        let cart_list = {
          storeCartGroupList: [
            {
              promotionCartGroupList: [
                {
                  cartList: [
                    {
                      buyNum: this.currentSpecNum,
                      goodsId: this.goodsId - 0,
                      productId: this.productId - 0,
                      productImage: (this.defaultProduct && this.defaultProduct.goodsPics && this.defaultProduct.goodsPics[0]) || '',
                      goodsName: this.goodsData.goodsName,
                      isChecked: 1,
                      productPrice: this.defaultProduct.productPrice,
                      productStock: this.defaultProduct.productStock,
                      specValues: specValues,
                    },
                  ],
                },
              ],
              storeId: this.storeInf.storeId,
              storeName: this.storeInf.storeName,
              checkedAll: true,
            },
          ],
          checkedAll: true,
          invalidList: [],
        };
        //未登录加入本地缓存
        let local_cart_list =
          this.goodsData.shopType == 2
            ? uni.getStorageSync("o2o_cart_list")
            : uni.getStorageSync("cart_list"); //购物车列表本地缓存
        if (local_cart_list) {
          //如果不是第一次存储
          let tmp_list1 = [];
          let tmp_list2 = [];
          cart_list.storeCartGroupList.forEach((item) => {
            item.promotionCartGroupList.forEach((item1) => {
              item1.cartList.forEach((item2) => {
                local_cart_list.storeCartGroupList.forEach((v) => {
                  v.promotionCartGroupList.forEach((v1) => {
                    v1.cartList.forEach((v2) => {
                      if (
                        v2.productId == item2.productId &&
                        v.storeId == item.storeId
                      ) {
                        tmp_list1.push(v);
                      }
                    });
                  });
                });
                tmp_list2 = local_cart_list.storeCartGroupList.filter((v) => {
                  return v.storeId == item.storeId;
                });
              });
            });
          });
          if (tmp_list1.length > 0 && tmp_list2.length > 0) {
            //同一店铺同一商品
            local_cart_list.storeCartGroupList.map((item) => {
              item.promotionCartGroupList.map((item1) => {
                item1.cartList.map((item2) => {
                  if (
                    item2.productId == this.productId &&
                    this.storeInf &&
                    item.storeId == this.storeInf.storeId
                  ) {
                    item2.buyNum += this.currentSpecNum;
                  }
                });
              });
            });
          } else if (tmp_list1.length == 0 && tmp_list2.length > 0) {
            //同一店铺不同商品
            local_cart_list.storeCartGroupList.map((item) => {
              if (item.storeId == this.storeInf.storeId) {
                item.promotionCartGroupList.map((item2) => {
                  item2.cartList.push(
                    cart_list.storeCartGroupList[0].promotionCartGroupList[0]
                      .cartList[0]
                  );
                });
              }
            });
          } else {
            //不同店铺不同商品
            local_cart_list.storeCartGroupList.push(
              cart_list.storeCartGroupList[0]
            );
          }

          // 未登录购物车展示数据
          uni.setStorage({
            key: this.goodsData.shopType == 2 ? "o2o_cart_list" : "cart_list",
            data: local_cart_list,
            success: function () {
              //更新购物车数和购物车数据
            },
          });
        } else {
          uni.setStorage({
            key: this.goodsData.shopType == 2 ? "o2o_cart_list" : "cart_list",
            data: cart_list,
            success: function () {
              //更新购物车数量和购物车数据
            },
          });
        }
        uni.showToast({
          title: this.$L("加入购物车成功！"),
          icon: "none",
          duration: 700,
        });
        this.$sldStatEvent({
          behaviorType: "cart",
          goodsId: this.goodsId,
          storeId: this.storeInf.storeId,
        });
        this.$refs.specModel.close();
        this.getNoLoginCartNum();
      } else {
        //已登录
        this.$request({
          url: "v3/business/front/cart/add",
          data: {
            productId: this.productId,
            number: this.currentSpecNum,
          },
          method: "POST",
        }).then((res) => {
          if (res.state == 200) {
            //更新购物车数量
            this.$refs.specModel.close();
            this.$sldStatEvent({
              behaviorType: "cart",
              goodsId: this.goodsId,
              storeId: this.storeInf.storeId,
            });
            this.$api.msg(res.msg);
            this.getCartNum();
          } else {
            this.$api.msg(res.msg);
          }
        });
      }
    },

    getUncheckedSpecs() {
      for (let item of this.specs) {
        let isCheck = item.specValueList.some((i) => i.checkState == 1);
        if (!isCheck) {
          return item;
        }
      }
      return null;
    },

    //确认下单
    buy(arg) {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin();
        return;
      } else {
        if (this.specRestrict) {
          let specs = this.getUncheckedSpecs();
          this.$api.msg(`${this.$L("请选择")}${specs.specName}`, "error");
          return;
        }

        this.editNum("edit");
        this.$refs.specModel.close();

        let data = {
          isCart: false,
          productId: this.productId,
          number: this.currentSpecNum,
          source: 1,
          productPrice:
            this.userCenterData.isSuper == 1 && this.defaultProduct.superPrice
              ? this.defaultProduct.superPrice
              : this.changePrice,
        };

        let { promotionId, promotionType } = this.defaultProduct;

        if (promotionId && promotionType && this.actiState) {
          data.promotionId = promotionId;
          data.promotionType = promotionType;
        }

        if (this.valiInfo(this.pinInfo) && this.pinButState == 0) {
          data.productPrice = this.defaultProduct.productPrice;
          delete data.promotionId;
          delete data.promotionType;
        }

        if (this.valiInfo(this.pinInfo) && this.pinButState == 1) {
          data.productPrice =
            this.pinInfo.leaderIsPromotion == 1
              ? this.pinInfo.leaderPrice
              : this.pinInfo.spellPrice;
        }

        if (this.valiInfo(this.pinInfo) && this.pinButState == 3) {
          data.spellTeamId = this.spellTeamId;
        } else {
          delete data.spellTeamId;
        }

        let param = {
          url: "v3/business/front/orderOperate/check",
          method: "POST",
          header: {
            "Content-Type": "application/json",
          },
          data,
        };

        if (typeof arg == "string" && arg == "aloneBuy") {
          param.data.isAloneBuy = true;
        }
        uni.showLoading();
        uni.setStorageSync("addressId", this.sourceId);
        this.$request(param).then((res) => {
          uni.hideLoading();
          if (res.state == 200) {
            //从这里跳转页面后置为1，从一个页面返回回来，将在onShow里调用getGoodsDetail
            this.showState = 1;
            let query = {
              orderType: 1,
              goodsId: this.goodsId,
              productId: this.productId,
              numbers: this.currentSpecNum,
              ifcart: 2,
            };
            if (this.valiInfo(this.pinInfo) && this.pinButState == 0) {
              query.isAloneBuy = true;
            } else if (this.valiInfo(this.pinInfo) && this.pinButState == 1) {
              query.isAloneBuy = false;
            } else if (this.valiInfo(this.pinInfo) && this.pinButState == 3) {
              query.isAloneBuy = false;
              query.spellTeamId = this.spellTeamId;
            }
            this.$Router.push({
              path: "/pages/order/confirmOrder",
              query,
            });
          } else if (res.state == 267) {
            this.exceptionProList = res.data.productList;
            this.exState = res.data.state;
            this.exStateTxt = res.data.stateValue;
            if (this.exState == 7) {
              this.$refs.purchasePop.open(0);
            } else if (this.exState == 5) {
              this.$refs.purchasePop.open(1);
            } else {
              this.$api.msg(res.data.stateValue);
            }
          } else {
            this.$api.msg(res.msg);
          }
        });
      }
    },
    moveHandle() { },

    goNext() {
      this.showState = 1;
      let query = {
        orderType: 1,
        goodsId: this.goodsId,
        productId: this.productId,
        numbers: this.currentSpecNum,
        ifcart: 2,
      };
      if (this.valiInfo(this.pinInfo) && this.pinButState == 0) {
        query.isAloneBuy = true;
      } else if (this.valiInfo(this.pinInfo) && this.pinButState == 1) {
        query.isAloneBuy = false;
      } else if (this.valiInfo(this.pinInfo) && this.pinButState == 3) {
        query.isAloneBuy = false;
        query.spellTeamId = this.spellTeamId;
      }

      this.$Router.push({
        path: "/pages/order/confirmOrder",
        query,
      });
    },

    confirmLogin() {
      let url = this.$Route.path;
      const query = this.$Route.query;
      this.$refs.popLogin.close();
      uni.setStorageSync("fromurl", {
        url,
        query,
      });
      this.$Router.push("/pages/public/login");
    },

    //收藏、取消收藏事件
    collectGoods() {
      if (!this.hasLogin) {
        //提示陆
        this.$refs.popLogin.open();
        return;
      } else {
        this.$request({
          url: "v3/member/front/followProduct/update",
          data: {
            productIds: this.defaultProduct.productId,
            isCollect: !this.goodsData.followGoods,
          },
          method: "POST",
        }).then((res) => {
          if (res.state == 200) {
            this.goodsData.followGoods =
              this.goodsData.followGoods == undefined
                ? true
                : !this.goodsData.followGoods;
            if (this.goodsData.followGoods) {
              this.$sldStatEvent({
                behaviorType: "fav",
                goodsId: this.goodsId,
                storeId: this.storeInf.storeId,
              });
            }
            this.$api.msg(res.msg);
          } else {
            this.$api.msg(res.msg);
          }
        });
      }
    },

    //去商品评价页面
    goEvaluation() {
      this.$Router.push({
        path: "/standard/product/evaluation",
        query: {
          productId: this.productId,
        },
      });
    },
    //服务弹框
    serviceModel() {
      this.$refs.serviceModel.open();
    },
    //满优惠弹框
    openFullDisModel() {
      this.$refs.fullDisModel.open();
    },
    //关闭弹框
    closeModel() {
      this.$refs.serviceModel.close();
      this.$refs.couponModel.close();
      this.$refs.fullDisModel.close();
    },
    //获取商品详情页，店铺优惠券
    getCouponList() {
      if (!this.goodsData?.storeInf?.storeId) {
        return
      }
      console.log("获取优惠券列表"); // 调试日志
      this.$request({
        url: "v3/promotion/front/coupon/storeCouponList",
        data: {
          storeId: this.goodsData.storeInf.storeId,
          current: this.pageCurrent,
          pageSize: this.pageSize,
          goodsId: this.goodsId,
        },
      }).then((res) => {
        if (res.state == 200) {
          this.couponList = res.data.list;
          this.couponHasReceiveCount = 0;

          // 检查是否所有可领取的优惠券都已领取
          const hasReceivableCoupons = this.couponList.some(
            (coupon) => coupon.isReceive === 1
          );

          for (const coupon of this.couponList) {
            console.log(coupon, coupon.isReceive);
            if (coupon.isReceive != 3) {
              this.couponHasReceiveCount++;
            }
          }

          // 如果是人气卡且有可领取的优惠券，执行自动领取
          if (
            this.isPopularityCard &&
            this.hasLogin &&
            !this.autoReceiveExecuted &&
            hasReceivableCoupons
          ) {
            this.autoReceiveExecuted = true;
            this.autoReceiveCoupons();
          }

          // 更新优惠券显示样式
          this.couponList.forEach((item) => {
            item.isOpen = false;
            if (item.isReceive == 3) {
              item.couponBg = this.finishReceiveBg;
            } else if (item.isReceive == 2) {
              item.couponBg = this.hasReceiveBg;
            } else if (item.isReceive == 1) {
              item.couponBg = this.goReceiveBg;
            }
          });
        } else {
          //错误提示
          this.$api.msg(res.msg);
        }
      });
    },
    //领券弹框
    openCouponModel() {
      this.$refs.couponModel.open();
    },
    //规则展开
    descriptionOpen(couponId) {
      this.couponList.map((item) => {
        if (item.couponId == couponId) {
          if (item.description != "") {
            item.isOpen = !item.isOpen;
            this.$forceUpdate();
          }
        }
      });
    },
    //立即领取
    goReceive(item) {
      if (!this.hasLogin) {
        this.$refs.popLogin.open();
        return;
      }
      let couponId = item.couponId;
      let param = {};
      param.url = "v3/promotion/front/coupon/receiveCoupon";
      param.method = "GET";
      param.data = {};
      param.data.couponId = couponId;
      this.$request(param)
        .then((res) => {
          if (res.state == 200) {
            let result = res.data;
            this.$api.msg(this.$L("领取成功!"));
            this.getCouponList();
            if (item.couponType == 3) {
              //随机优惠券
              this.rondomMod = true;
              this.rondomDes = result;
            }
          } else {
            this.$api.msg(res.msg);
            this.getCouponList();
          }
        })
        .catch((e) => {
          //异常处理
        });
    },
    //去我的优惠券列表页面
    goMyCoupon() {
      this.$Router.push("/standard/coupon/myCoupon");
      this.rondomMod = false;
    },
    //关闭随机优惠券弹窗
    closeCoupon() {
      this.rondomMod = false;
    },
    //获取满优惠列表
    fullDiscountList() {
      let that = this;
      let param = {};
      param.url = "v3/goods/front/goods/activityList";
      param.method = "GET";
      param.data = {};
      param.data.productId = this.productId;
      this.$request(param)
        .then((res) => {
          if (res.state == 200) {
            let result = res.data;
            this.fullDisList = result;
            result.map((item) => {
              // item.descriptionList = item.descriptionList ? quillEscapeToHtml(item.descriptionList) : ''
              item.descriptionList = item.descriptionList.map((i) => {
                i = i.replace(
                  /<(.+?)>/g,
                  (num) =>
                    "<text style='color:var(--color_main)'>" +
                    num.slice(1, num.length - 1) +
                    "</text>"
                );
                i = i.replace(
                  /x[\d]/g,
                  (num) =>
                    "<text style='color:var(--color_main)'>" + num + "</text>"
                );
                return `${i} ;`;
              });
            });
          } else {
            this.$api.msg(res.msg);
          }
        })
        .catch((e) => {
          //异常处理
        });
    },
    //获取店铺推荐商品信息
    getRecommend() {
      let that = this;
      let param = {};
      param.url = "v3/goods/front/goods/recommendList";
      param.method = "GET";
      param.data = {};
      param.data.queryType = "goods";
      param.data.productId = this.productId;
      this.$request(param).then((res) => {
        if (res.state == 200) {
          let result = res.data;
          this.recommendedList = result.list;
          this.getSelectorQuery();
        } else {
          this.$api.msg(res.msg);
        }
      });
    },

    //获取店铺推荐文章信息
    getRecommendVideo() {
      let param = {};
      param.url = "v3/video/front/video/recommendList";
      param.method = "GET";
      param.data = {};
      param.data.queryType = "video";
      param.data.pageSize = 3;
      param.data.goodsId = this.goodsId;
      this.$request(param).then((res) => {
        if (res.state == 200) {
          let result = res.data;
          this.recommendedVideoList = result.videoList;
          this.getSelectorQuery();
        } else {
          this.$api.msg(res.msg);
        }
      });
    },

    //打开规格弹框
    showSpecModel(type) {
      //如果是购买操作，并且商品总库存为0
      if (
        (type === "buy" || typeof type !== "string") &&
        !this.defaultProduct.productStock
      ) {
        return;
      }
      if (type == "add") {
        this.showSpecModelType = "add";
      } else if (type == "buy") {
        this.showSpecModelType = "buy";
      } else if (type == "offshelf") {
        this.showSpecModelType = "offshelf";
      } else if (type == "nosocket") {
        this.showSpecModelType = "nosocket";
      } else if (type == "pinAlone" && this.valiInfo(this.pinInfo)) {
        this.pinButState = 0;
      } else if (type == "pinLeague" && this.valiInfo(this.pinInfo)) {
        this.pinButState = 1;
      } else if (!type && this.valiInfo(this.pinInfo)) {
        this.pinButState = 2;
      } else if (type == "joinLeague") {
        this.pinButState = 3;
      } else {
        this.showSpecModelType = "";
      }
      this.$forceUpdate();
      this.$refs.specModel.open();
    },
    //统一处理活动商品的数量的加及编辑
    activityAddEdit(type) {
      let that = this;

      that.currentSpecNum = that.currentSpecNum.toString().replace(/\D/g, "");
      if (that.currentSpecNum == "" || that.currentSpecNum < 0) {
        setTimeout(() => {
          that.currentSpecNum = 1;
        }, 0);
        return;
      }

      let activityLimitNumber = 0; //活动的限购量 0代表不限购
      let activityProductStock = this.defaultProduct.productStock; //活动的库存

      if (that.secKillInfo && that.secKillInfo.state == 2) {
        //秒杀活动进行中
        activityLimitNumber = that.secKillInfo.upperLimit;
      } else if (
        this.valiInfo(that.preSellInfo) &&
        that.preSellInfo.startTime
      ) {
        //预售进行中
        activityLimitNumber = that.preSellInfo.buyLimit;
      } else if (this.valiInfo(that.pinInfo)) {
        activityLimitNumber = that.pinInfo.buyLimit;
      } else if (this.valiInfo(that.ladderInfo)) {
        activityLimitNumber = that.ladderInfo.buyLimitNum;
      }
      if (
        activityLimitNumber < activityProductStock &&
        activityLimitNumber > 0
      ) {
        // 限购数 < 库存
        if (that.currentSpecNum >= activityLimitNumber) {
          setTimeout(() => {
            that.currentSpecNum = activityLimitNumber;
            that.noEdit = true;
          }, 0);
        } else {
          setTimeout(() => {
            type == "add" ? that.currentSpecNum++ : that.currentSpecNum;
          }, 0);
          that.noEdit = false;
        }
      } else {
        //限购数 > 库存
        if (that.currentSpecNum < activityProductStock) {
          if (that.currentSpecNum == 0 || that.currentSpecNum < 0) {
            setTimeout(() => {
              that.currentSpecNum = 1;
            }, 0);
          } else {
            if (that.currentSpecNum > 99999) {
              setTimeout(() => {
                that.currentSpecNum = 99999;
              }, 0);
              that.noEdit = true;
            } else {
              setTimeout(() => {
                type == "add" ? that.currentSpecNum++ : that.currentSpecNum;
              }, 0);
              that.noEdit = false;
            }
          }
        } else {
          setTimeout(() => {
            that.currentSpecNum = activityProductStock;
          }, 0);
          that.noEdit = true;
        }
      }
      this.getUserSaveAmount(this.cityCode);
      this.getUserEx(this.cityCode);
    },

    //编辑数量
    editNum(type, e) {
      let that = this;
      let reg = /\./g;
      let reg0 = /0+\d/;

      if (
        reg.test(this.currentSpecNum.toString()) ||
        this.currentSpecNum <= 0
      ) {
        setTimeout(() => {
          that.currentSpecNum = 1;
        }, 0);
      }

      if (type == "add") {
        if (this.actiState) {
          that.activityAddEdit("add");
        } else {
          let productStock = this.defaultProduct.productStock;
          let limit = Math.min(productStock, 99999);
          this.currentSpecNum++;
          if (this.currentSpecNum > limit) {
            this.currentSpecNum = limit;
            this.noEdit = true;
          }
        }
      } else if (type == "edit") {
        if (that.currentSpecNum == "" || that.currentSpecNum < 0) {
          setTimeout(() => {
            that.currentSpecNum = 1;
          }, 0);
          return;
        }
        if (this.actiState) {
          that.activityAddEdit("edit");
        } else {
          if (that.currentSpecNum > that.defaultProduct.productStock) {
            setTimeout(() => {
              this.currentSpecNum = that.defaultProduct.productStock;
            }, 0);
            that.noEdit = true;
            return;
          } else {
            that.currentSpecNum =
              e && e.detail.value ? e.detail.value : that.currentSpecNum;
            if (that.currentSpecNum == 0 || that.currentSpecNum < 0) {
              setTimeout(() => {
                that.currentSpecNum = 1;
              }, 0);
              return;
            } else {
              that.currentSpecNum = that.currentSpecNum
                .toString()
                .replace(/\D/g, "");
              if (that.currentSpecNum > 99999) {
                setTimeout(() => {
                  that.currentSpecNum = 99999;
                }, 0);
                that.noEdit = true;
                return;
              } else {
                setTimeout(() => {
                  that.currentSpecNum = Number(that.currentSpecNum);
                }, 0);
                that.noEdit = false;
              }
            }
          }
        }
      } else if (type == "reduce") {
        if (that.currentSpecNum > 1) {
          that.currentSpecNum--;
          that.noEdit = false;
        } else {
          that.currentSpecNum = 1;
        }
      }
      this.getUserSaveAmount(this.cityCode);
      this.getUserEx(this.cityCode);
    },
    //关闭规格弹框
    closeSpecModel() {
      this.$refs.specModel.close();
    },
    //去商品详情页面
    goGoodsDetail(defaultProductId, goodsId) {
      this.$Router.push({
        path: "/standard/product/detail",
        query: {
          productId: defaultProductId,
          goodsId,
        },
      });
    },
    //获取购物车数据
    async getCartNum() {
      const res = await this.$getCartNum();
      this.cartNum = res;
      this.cartNumShow = true;
    },

    //去店铺页面
    goShopHome() {
      this.showState = 1;

      if (this.goodsData.shopType != 2) {
        this.$Router.push({
          path: "/standard/store/shopHomePage",
          query: {
            vid: this.goodsData.storeInf.storeId,
          },
        });
        return;
      }
    },
    //去店铺商品列表页面
    toStoreGoodList() {
      this.$Router.push({
        path: "/standard/store/shopHomePage",
        query: {
          vid: this.goodsData.storeInf.storeId,
          goods_list: 1,
        },
      });
    },
    //分享链接的弹框展示
    tipsShow() {
      this.tips_show = !this.tips_show;
      this.transparent_mask = !this.transparent_mask;
    },
    //三点分享链接
    handleLink(e) {
      let link = e.currentTarget.dataset.link;
      let type = e.currentTarget.dataset.type;
      if (type != "share") {
        // wx.switchTab({
        // 	url: link
        // });
        this.$Router.pushTab(link);
      }
      this.tips_show = false;
    },
    //隐藏透明遮罩层
    hideMask() {
      if (this.copyname_now) {
        if (!this.copyname_go) {
          return;
        }
        this.copyname_now = false;
      } else {
        this.tips_show = false;
      }
      this.transparent_mask = false;
    },

    //展开规格参数
    handleGoodsParam() {
      this.openGoodsParam = !this.openGoodsParam;
    },
    //去商品详情页
    goProductDetail(defaultProductId) {
      this.$Router.push({
        path: "/standard/product/detail",
        query: {
          productId: defaultProductId,
        },
      });
    },

    valiInfo(info) {
      return JSON.stringify(info) != "{}";
    },
    //h5转新页面播视频
    toPlayPage() {
      uni.navigateTo({
        url:
          "/standard/product/video?video_url=" +
          this.goodsVideo +
          "&posterImage=" +
          ((this.defaultProduct && this.defaultProduct.goodsPics && this.defaultProduct.goodsPics[0]) || ''),
      });
    },

    //获取地址列表
    getAddressList() {
      this.$request({
        url: "v3/member/front/memberAddress/list",
        method: "GET",
      }).then((res) => {
        if (res.state == 200) {
          if (res.data.list.length > 0) {
            this.addressList = res.data.list = res.data.list;
            if (this.addressList.findIndex((i) => i.isDefault == 1) > 0) {
              let index = this.addressList.findIndex((i) => i.isDefault == 1);
              this.curAddress =
                this.addressList[index].addressAll +
                "" +
                this.addressList[index].detailAddress;
              this.sourceId = this.addressList[index].addressId;
              this.cityCode = this.addressList[index].cityCode;
            } else {
              this.curAddress =
                this.addressList[0].addressAll +
                "" +
                this.addressList[0].detailAddress;
              this.sourceId = this.addressList[0].addressId;
              this.cityCode = this.addressList[0].cityCode;
            }
            if (uni.getStorageSync("addressId")) {
              let addressID = uni.getStorageSync("addressId");
              if (res.data.list.filter((i) => i.addressId == addressID)[0]) {
                let tmp = res.data.list.filter(
                  (i) => i.addressId == addressID
                )[0];
                this.curAddress = tmp.addressAll + "" + tmp.detailAddress;
                this.sourceId = tmp.addressId;
                this.cityCode = tmp.cityCode;
              }
            }
            this.getUserEx(this.cityCode);
          }
        }
      });
    },
    checkAddress(item) {
      this.sourceId = item.addressId;
      this.$refs.addressModel.close();
      this.curAddress = item.addressAll + "" + item.detailAddress;
      uni.setStorageSync("addressId", this.sourceId);
      this.cityCode = item.cityCode;
      this.getUserEx(this.cityCode);
      this.getUserSaveAmount(this.cityCode);
    },
    addressClose() {
      this.$refs.addressModel.close();
    },
    //用于切换地址，获取运费
    getUserEx(cityCode) {
      let data = {
        productId: this.productId,
        num: this.currentSpecNum,
      };
      cityCode && (data.cityCode = cityCode);

      this.$request({
        url: "v3/goods/front/goods/calculateExpress",
        data,
      }).then((res) => {
        if (res.state == 200) {
          this.expressFee = res.data;
        } else {
          this.$api.msg(res.msg);
        }
      });
    },
    //用于数量变化或地址变化时的省钱计算
    async getUserSaveAmount(cityCode) {
      this.saveAmount = await getSaveAmount(
        cityCode,
        this.productId,
        this.currentSpecNum
      );
    },

    //预览产品图片
    previewImg(current) {
      let _this = this;
      uni.previewImage({
        urls: (this.defaultProduct && this.defaultProduct.goodsPics) || [],
        current,
        longPressActions: {
          success: function (data) {
            uni.saveImageToPhotosAlbum({
              filePath: ((this.defaultProduct && this.defaultProduct.goodsPics && this.defaultProduct.goodsPics[data.index]) || ''),
              success: function () {
                _this.$api.msg(_this.$L("保存成功"));
              },
              fail: function (err) {
                _this.$api.msg(err.errMsg);
              },
            });
          },
          fail: function (err) {
            _this.$api.msg(err);
          },
        },
      });
    },
    //复制产品名称
    openCopyname() {
      this.copyname_go = false;
      this.copyname_now = true;
      this.transparent_mask = true;
    },
    copyOpenEnd() {
      setTimeout(() => {
        this.copyname_go = true;
      }, 300);
    },
    copyName() {
      let data = this.goodsData.goodsName;
      uni.setClipboardData({
        data,
        success: () => {
          this.transparent_mask = false;
          this.copyname_now = false;
          this.$api.msg(this.$L("已复制到剪切板"));
        },
      });
    },
    //通超级会员
    getSuper() {
      if (this.userCenterData && !this.userCenterData.isSuper) {
        this.$Router.push({
          path: "/standard/super/index",
          query: {
            type: 0,
          },
        });
      }
    },

    // 预览规格图片
    previewSpecImage(image) {
      if (!image) return;

      uni.previewImage({
        urls: [image],
        current: 0,
        longPressActions: {
          success: function (data) {
            uni.saveImageToPhotosAlbum({
              filePath: image,
              success: function () {
                this.$api.msg(this.$L("保存成功"));
              },
              fail: function (err) {
                this.$api.msg(err.errMsg);
              },
            });
          },
          fail: function (err) {
            this.$api.msg(err);
          },
        },
      });
    },

    // 处理优惠券点击
    handleCouponClick() {
      // 人气卡商品不弹出优惠券窗口
      if (this.isPopularityCard) {
        return;
      }
      this.openCouponModel();
    },

    // 自动领取优惠券
    async autoReceiveCoupons() {
      if (!this.couponList?.length) {
        console.log("没有可领取的优惠券"); // 调试日志
        return;
      }
      // 检查是否所有优惠券都已领取
      const allReceived = this.couponList.every(
        (coupon) => coupon.isReceive === 2 || coupon.isReceive === 3
      );

      if (allReceived) {
        uni.showToast({
          title: this.$L("自动领取可用优惠券！"),
          icon: "none",
          duration: 3000,
        });
        return;
      }

      try {
        let goReceiveCount = 0;

        for (let coupon of this.couponList) {
          if (coupon.isReceive !== 1) continue;

          const res = await this.$request({
            url: "v3/promotion/front/coupon/receiveCoupon",
            method: "GET",
            data: {
              couponId: coupon.couponId,
            },
          });

          if (res.state === 200) {
            goReceiveCount++;

            // 如果是随机优惠券
            if (coupon.couponType == 3 && res.data) {
              this.rondomMod = true;
              this.rondomDes = res.data;
            }
          } else {
            this.$api.msg(res.msg);
          }
        }
        if (goReceiveCount > 0) {
          // 显示统一的领取成功提示
          uni.showToast({
            title: this.$L(`自动领取${goReceiveCount}张可用优惠券`),
            icon: "none",
            duration: 700,
          });

          // 刷新优惠券列表
          this.getCouponList();
        }
      } catch (e) {
        this.$api.msg(e.msg || "领取失败");
      }
    },
    // 新增回首页按钮
    goHome() {
      console.log('详情跳转首页');
      // #ifdef MP
      uni.switchTab({
        url: '/pages/index/index'
      })
      // #endif

      // #ifndef MP
      this.$Router.push("/pages/index/index");
      // #endif
    },
    goInformation() {
      this.$nextTick(() => {
        uni.reLaunch({
          url: `/pages/index/information?goodsId=${this.goodsId}`,
          success: () => {
            console.log('跳转成功');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            this.$api.msg('页面跳转失败');
          }
        });
      });
    },
    goPublish() {
      uni.navigateTo({
        url: `/pages/graphic/graphicRelease?goodsId=${this.goodsId}&labelId=2`,
        fail: (err) => {
          console.error('跳转失败:', err);
          this.$api.msg('页面跳转失败');
        }
      });
    },
  },
};
</script>

<style lang="scss">
page {
  background: $bg-color-split;
  width: 750rpx;
  margin: 0 auto;
}

button::after {
  border: none;
}

.container {
  position: relative;
}

.go_back {
  width: 50rpx;
  height: 50rpx;
  position: absolute;
  /* #ifdef H5 */
  top: 28rpx;
  /* #endif */
  //app-4-start

  //app-4-end
  left: 25rpx;
  z-index: 99;

  image {
    width: 50rpx;
    height: 50rpx;
  }
}

.go_more {
  width: 50rpx;
  height: 50rpx;
  position: absolute;
  /* #ifdef H5 */
  top: 28rpx;
  /* #endif */
  //app-5-start

  //app-5-end
  right: 25rpx;
  z-index: 99;

  image {
    width: 50rpx;
    height: 50rpx;
  }

  .triangle-up {
    position: absolute;
    display: block;
    top: 40rpx;
    right: -15rpx;
    width: 0rpx;
    height: 0rpx;
    background: #ffffff;
    border: 1px solid #cccccc;

    &::before {
      box-sizing: content-box;
      width: 0;
      height: 0;
      position: absolute;
      top: -2px;
      right: 25rpx;
      padding: 0;
      border-bottom: 8px solid #ffffff;
      border-top: 8px solid transparent;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      display: block;
      content: "";
      z-index: 12;
    }

    &::after {
      box-sizing: content-box;
      width: 0px;
      height: 0px;
      position: absolute;
      top: -5px;
      right: 20rpx;
      padding: 0;
      border-bottom: 9px solid rgba(102, 102, 102, 0.1);
      border-top: 9px solid transparent;
      border-left: 9px solid transparent;
      border-right: 10px solid transparent;
      display: block;
      content: "";
      z-index: 10;
    }
  }

  .tips {
    position: absolute;
    z-index: 20;
    top: 70rpx;
    right: -10rpx;
    width: 226rpx;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 0px 10rpx 0px rgba(102, 102, 102, 0.2);
    opacity: 0.94;
    border-radius: 15rpx;
    display: flex;
    flex-direction: column;

    .tips_pre {
      width: 100%;
      height: 88rpx;
      display: flex;
      align-items: center;
      padding-left: 40rpx;
      box-sizing: border-box;
      border: none;
    }

    button::after {
      border: none;
      border-bottom: 1rpx solid #f1f1f1;
    }

    button[plain] {
      border: none;
    }

    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 20rpx;
    }

    text {
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(51, 51, 51, 1);
      line-height: 32rpx;
    }
  }
}

.icon-you {
  font-size: $font-base;
  color: #888;
}

/* 透明遮罩层 */
.transparent_mask {
  width: 100%;
  height: 100%;
  position: fixed;
  background: rgba(0, 0, 0, 1);
  opacity: 0.4;
  top: 0;
  left: 0;
  z-index: 10;
}

.fixed_top_status_bar {
  position: fixed;
  //app-6-start

  //app-6-end

  height: 0;

  top: 0;
  left: 0;
  right: 0;
  z-index: 99;
  background: rgba(250, 250, 250, 0.9);
}

.fixed_top_status_bar_no_opcity {
  background: #ffffff;
}

.nav_list_boxz {
  position: fixed;
  width: 750rpx;
  background: rgba(250, 250, 250, 0.9);
  padding: 0 50rpx 0 20rpx;
  //wx-9-start
  /* #ifdef MP */
  padding-left: 20rpx;
  /* #endif */
  //wx-9-end
  align-items: center;
  z-index: 50;
}

.nav_list {
  display: flex;
  justify-content: space-between;
  // position: fixed;
  // width: 750rpx;
  height: 100rpx;
  // background: rgba(250, 250, 250, 0.9);
  // padding: 0 50rpx 0 20rpx;
  //wx-10-start
  /* #ifdef MP */
  // padding-left: 20rpx;
  /* #endif */
  //wx-10-end
  align-items: center;
  z-index: 50;

  .go_back_nav {
    width: 50rpx;
    height: 50rpx;

    image {
      width: 20rpx;
      height: 32rpx;
    }
  }

  .nav_list_pre {
    font-size: 32rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #333333;
    line-height: 32rpx;
    padding-bottom: 5rpx;
  }

  .nav_list_pre_active {
    border-bottom: 5rpx solid var(--color_main);
  }

  /* 三点更多分享 */
  .more_tips {
    position: relative;
    display: flex;
    align-items: center;

    .more {
      width: 50rpx;
      height: 50rpx;
    }

    .triangle-up {
      position: absolute;
      display: block;
      top: 40rpx;
      left: 25rpx;
      width: 0rpx;
      height: 0rpx;
      background: #ffffff;
      border: 1px solid #cccccc;

      &::before {
        box-sizing: content-box;
        width: 0;
        height: 0;
        position: absolute;
        top: -2px;
        right: 25rpx;
        padding: 0;
        border-bottom: 8px solid #ffffff;
        border-top: 8px solid transparent;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        display: block;
        content: "";
        z-index: 12;
      }

      &::after {
        box-sizing: content-box;
        width: 0px;
        height: 0px;
        position: absolute;
        top: -5px;
        right: 20rpx;
        padding: 0;
        border-bottom: 9px solid rgba(102, 102, 102, 0.1);
        border-top: 9px solid transparent;
        border-left: 9px solid transparent;
        border-right: 10px solid transparent;
        display: block;
        content: "";
        z-index: 10;
      }
    }

    .tips {
      position: absolute;
      z-index: 20;
      top: 70rpx;
      right: -30rpx;
      width: 226rpx;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 0px 10rpx 0px rgba(102, 102, 102, 0.2);
      opacity: 0.94;
      border-radius: 15rpx;
      display: flex;
      flex-direction: column;

      .tips_pre {
        width: 100%;
        height: 88rpx;
        display: flex;
        align-items: center;
        padding-left: 40rpx;
        box-sizing: border-box;
      }

      button::after {
        border: none;
        border-bottom: 1rpx solid #f1f1f1;
      }

      button[plain] {
        border: none;
      }

      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 20rpx;
      }

      text {
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: rgba(51, 51, 51, 1);
        line-height: 32rpx;
      }
    }
  }
}

.nav_list_no_opcity {
  // background: #ffffff;
}

.carousel {
  height: 750rpx;
  position: relative;
  //app-7-start

  //app-7-end
  .swiper-box {
    width: 750rpx;
    height: 750rpx;
  }

  swiper {
    height: 100%;
  }

  .image-wrapper {
    width: 100%;
    height: 100%;
  }

  .swiper-item {
    display: flex;
    justify-content: center;
    align-content: center;
    height: 750rpx;
    overflow: hidden;

    image {
      max-width: 100%;
      max-height: 100%;
    }
  }
}

/* 拼团购买按钮start */
.group_shopping {
  display: flex;
  align-items: center;
}

.pinGroup_btn {
  display: flex;
}

.group_shopping_alone {
  width: 223rpx;
  height: 70rpx;
  background: var(--color_spell_vice);
  border-radius: 34rpx 0 0 34rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.group_alone_price {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
  line-height: 30rpx;
}

.group_alone_title {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
  line-height: 30rpx;
}

.go_group {
  width: 197rpx;
  height: 70rpx;
  background: var(--color_spell_main);
  border-radius: 0 34rpx 34rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.go_group_price {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
  line-height: 30rpx;
}

.go_group_title {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
  line-height: 30rpx;
}

.make_group {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}

.make_group_num {
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(45, 45, 45, 1);
  line-height: 45rpx;
}

.make_groip_more {
  display: flex;
  align-items: center;
}

.make_groip_more text {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(251, 27, 27, 1);
}

.make_groip_more image {
  width: 12rpx;
  height: 20rpx;
}

/* 拼团购买按钮end */

/* 秒杀活动 start */
.second_kill {
  width: 750rpx;
}

.second_kill_con_box {
  background: var(--color_seckill_main_bg);
}

.second_kill_con {
  width: 750rpx;
  height: 126rpx;
  padding-left: 114rpx;
  padding-right: 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: -2rpx;
}

.second_kill_left {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.second_kill_goods_price {
  font-size: 30rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.second_kill_goods_price text:nth-child(2) {
  font-size: 40rpx;
}

.second_kill_price {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #ffffff;
  opacity: 0.8;
}

.line_through {
  text-decoration: line-through;
}

.second_kill_right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.second_kill_text {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 600;
  color: #fff;
  margin-bottom: 20rpx;
}

.sec_kill_countdown {
  display: flex;
  align-items: center;
  font-size: 20rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #fff;
  line-height: 34rpx;
}

.sec_kill_countdown .day {
  margin-right: 10rpx;
}

.sec_kill_countdown .time {
  background: #fff;
  width: 34rpx;
  height: 34rpx;
  border-radius: 50%;
  line-height: 34rpx;
  text-align: center;
  color: var(--color_seckill_main);
}

.sec_kill_countdown .time_tips {
  color: #fff;
  margin: 0 5rpx;
}

.sec_kill_preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  box-sizing: border-box;
  width: 750rpx;
  height: 50rpx;
}

.sec_kill_preview_left {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #666666;
}

.sec_kill_preview_right {
  display: flex;
  align-items: center;
  width: 136px;
  height: 34px;
  background: var(--color_seckill_main);
  border-radius: 17px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0.5);
  margin-right: -34px;
}

.sec_kill_preview_right image {
  width: 46rpx;
  height: 48rpx;
  margin: 0 10rpx 0 10rpx;
}

.sec_kill_preview_right .tip {
  font-size: 24px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #ffffff;
  line-height: 24px;
  letter-spacing: 2rpx;
}

.cancel_preview {
  width: 136px;
  height: 34px;
  background: #999999;
  border-radius: 17px;
  font-size: 22px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  line-height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0.5);
  margin-right: -34px;
}

/* 秒杀活动 end */

/* 阶梯团活动 */
.ladder {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 140rpx;
  overflow: hidden;
  background: var(--color_ladder_main_bg);
}

/* 阶梯团活动end */

.addressDefault {
  color: #999999 !important;
}

/* 有活动的商品描述详情 start */
.introduce_section_activity {
  width: 100%;
  background: #ffffff;
  padding: 20rpx 24rpx;
  box-sizing: border-box;

  .activity_goods_des {
    .flex_row_between_center {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .activity_goods_name {
        font-size: 32rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 44rpx;
        flex: 1;
      }

      .activity_goods_sales {
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 44rpx;
        margin-left: 20rpx;
      }
    }

    .activity_share_collection {
      display: flex;
      align-items: center;
      margin-top: 20rpx;

      .activity_goods_collection,
      .activity_goods_share {
        display: flex;
        align-items: center;
        margin-right: 30rpx;

        .iconfont {
          font-size: 32rpx;
          margin-right: 8rpx;
        }

        .show_text {
          font-size: 24rpx;
          color: #666666;
        }
      }
    }
  }
}

/* 有活动的商品描述详情 end */

/* 标题简介 */
.introduce_section {
  background: #fff;
  padding: 20rpx;
  // margin-bottom: 20rpx;

  .price_part {
    .left {
      display: flex;
      flex-direction: column;

      .sell_price {
        color: var(--color_price);

        .unit {
          font-size: 26rpx;
          font-weight: bold;
        }

        .price_int {
          font-size: 50rpx;
          line-height: 50rpx;
          margin-left: 4rpx;
          font-weight: bold;
        }

        .price_decimal {
          font-size: 26rpx;
          font-weight: bold;
        }
      }

      .original_price {
        color: #949494;
        font-size: 22rpx;
        text-decoration: line-through;
      }
    }

    .right {
      .collection {
        display: flex;
        flex-direction: column;
        width: 72rpx;
        white-space: nowrap;

        .iconaixin1 {
          color: var(--color_main) !important;
        }

        .iconaixin {
          color: #2d2d2d;
        }

        text {
          font-size: 22rpx;
          font-weight: 500;
          color: #2d2d2d;
        }
      }

      view {
        .iconfont {
          font-size: 50rpx;
          color: #2d2d2d;

          &.active {
            color: var(--color_vice);
          }
        }

        .show_text {
          color: #2d2d2d;
          font-size: 22rpx;
          font-weight: 500;

          &.active {
            color: var(--color_main);
          }
        }

        &:last-child {
          margin-left: 25rpx;
        }
      }
    }
  }

  .price_super {
    width: 710rpx;
    height: 75rpx;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    margin: 24rpx auto 0;

    .iconziyuan11 {
      font-size: 20rpx;
      margin-left: 6rpx;
    }

    .price_super_tips {
      color: #f1c9a1;
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 400;

      span {
        color: #ff1815;
        margin-left: 10rpx;
        margin-right: 10rpx;
      }
    }

    .price_super_arrow {
      width: 30rpx;
      height: 30rpx;
      line-height: 26rpx;
      color: #a9794a;
      font-size: 24rpx;
      border-radius: 50%;
      text-align: center;
      background: linear-gradient(#d1a470, #aa763e);
      flex-shrink: 0;
      margin-left: 30rpx;
    }

    .price_super_arrow_simple {
      color: #a9794a;
      font-size: 24rpx;
      margin-left: 10rpx;
    }

    .price_super_logo {
      position: relative;
      bottom: 5rpx;
      width: 40rpx;
      height: 40rpx;
      margin-left: 30rpx;
      margin-right: 30rpx;
    }

    .price_super_tag {
      color: #7a5423;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: bold;
      margin-right: 30rpx;
    }

    .price_super_desc {
      color: #684625;
      font-size: 24rpx;
      font-family: PingFang SC;
      font-weight: 500;

      span {
        color: #ff1815;
        margin-left: 6rpx;
        margin-right: 6rpx;
      }
    }
  }

  .goods_name {
    position: relative;
    margin-top: 20rpx;
    min-width: 120rpx;

    &.widthIndent {
      text-indent: 120rpx;
    }

    text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      line-height: 45rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-word;
    }

    .copy_pop {
      position: absolute;
      top: -76rpx;
      right: -8rpx;
      z-index: 51;
      width: 140rpx;
      height: 68rpx;
      line-height: 68rpx;
      color: #fff;
      font-size: 28rpx;
      text-align: center;
      border-radius: 10rpx;
      background: #000;

      &:after {
        content: "";
        position: absolute;
        right: 56rpx;
        bottom: -14rpx;
        z-index: 51;
        width: 0;
        height: 0;
        border-top: 14rpx solid #000;
        border-left: 14rpx solid transparent;
        border-right: 14rpx solid transparent;
      }
    }
  }

  .goods_ad {
    color: #666666;
    font-size: 28rpx;
    line-height: 40rpx;
    margin-top: 20rpx;
  }

  .coupon-tip {
    align-items: center;
    padding: 4rpx 10rpx;
    background: $uni-color-primary;
    font-size: $font-sm;
    color: #fff;
    border-radius: 6rpx;
    line-height: 1;
    transform: translateY(-4rpx);
  }
}

.left_super {
  .sell_price {
    color: #242846 !important;
    font-weight: bold;

    .unit {
      font-size: 24rpx;
      color: #242846 !important;
      font-weight: bold !important;
    }

    .price_int {
      color: #242846 !important;
      font-size: 30rpx;
      margin-left: 4rpx;
      font-weight: bold !important;
    }

    .price_decimal {
      color: #242846 !important;
      font-size: 24rpx;
      font-weight: bold !important;
    }
  }

  .left_super_price_img {
    width: 102rpx;
    height: 34rpx;
    line-height: 36rpx;
    color: #cfb295;
    font-size: 20rpx;
    font-family: PingFang SC;
    font-weight: 500;
    text-align: center;
    text-indent: 6rpx;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    margin-left: 10rpx;
  }

  .super_original_price {
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #666;

    text:not(:nth-child(2)) {
      font-size: 22rpx;
    }
  }
}

/* 分享 */
.share-section {
  display: flex;
  align-items: center;
  color: $font-color-base;
  background: linear-gradient(to right, #fdf5f6, #fbebf6);
  padding: 12rpx 30rpx;

  .share-icon {
    display: flex;
    align-items: center;
    width: 70rpx;
    height: 30rpx;
    line-height: 1;
    border: 1px solid $uni-color-primary;
    border-radius: 4rpx;
    position: relative;
    overflow: hidden;
    font-size: 22rpx;
    color: $uni-color-primary;

    &:after {
      content: "";
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      left: -20rpx;
      top: -12rpx;
      position: absolute;
      background: $uni-color-primary;
    }
  }

  .icon-xingxing {
    position: relative;
    z-index: 1;
    font-size: 24rpx;
    margin-left: 2rpx;
    margin-right: 10rpx;
    color: #fff;
    line-height: 1;
  }

  .tit {
    font-size: $font-base;
    margin-left: 10rpx;
  }

  .icon-bangzhu1 {
    padding: 10rpx;
    font-size: 30rpx;
    line-height: 1;
  }

  .share-btn {
    flex: 1;
    text-align: right;
    font-size: $font-sm;
    color: $uni-color-primary;
  }

  .icon-you {
    font-size: $font-sm;
    margin-left: 4rpx;
    color: $uni-color-primary;
  }
}

.spec_con {
  padding: 16rpx 8rpx 16rpx 20rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;

  .spec_left {
    display: flex;
    /* align-items: center; */

    .spec_left_title {
      font-size: 28rpx;
      color: #666666;
      line-height: 45rpx;
      margin-right: 35rpx;
    }

    .spec_left_content {
      width: 550rpx;
      /* white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden; */
      word-break: break-all;
      font-size: 28rpx;
      font-family: PingFang SC;
      color: #343434;
      line-height: 45rpx;
      margin-right: 10rpx;
    }
  }

  .spec_right {
    width: 36rpx;
    height: 36rpx;
  }
}

.c-list {
  font-size: $font-sm;
  color: $font-color-base;
  background: #fff;

  .c-row {
    display: flex;
    align-items: center;
    padding: 20rpx 20rpx;
    position: relative;
  }

  .tit {
    color: #666;
    font-size: 26rpx;
    margin-right: 35rpx;
  }

  .con {
    flex: 1;
    color: #333;
    font-size: 28rpx;

    .selected-text {
      margin-right: 10rpx;
    }
  }

  .bz-list {
    height: 40rpx;
    font-size: $font-sm;
    color: $font-color-dark;

    text {
      display: inline-block;
      margin-right: 30rpx;
    }
  }

  .con-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    color: $font-color-dark;
    line-height: 40rpx;
  }

  .red {
    color: $uni-color-primary;
  }
}

/* 发货地址及运费 start */
.deliver_goods {
  background: #ffffff;
  border-top: 1px solid #f2f2f2;

  .deliver_goods_con {
    margin: 0 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .deliver_goods_left {
      display: flex;
      align-items: center;

      .deliver_goods_title {
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #666666;
        margin-right: 32rpx;
      }
    }

    .deliver_goods_address {
      height: 50rpx;
      display: flex;
      align-items: center;

      image {
        width: 34rpx;
        height: 38rpx;
        margin-right: 10rpx;
      }

      text {
        display: inline-block;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 45rpx;
        width: 252rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 10rpx;
      }
    }

    .deliver_goods_right_main {
      width: 610rpx;
      padding-top: 8rpx;
      padding-bottom: 10rpx;

      .deliver_goods_address {
        height: 60rpx;
        margin-bottom: 4rpx;

        text {
          width: 560rpx;
        }
      }

      .deliver_goods_right_bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .deliver_goods_center {
          margin-left: 44rpx;
        }
      }
    }

    .deliver_goods_center {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #2d2d2d;
      line-height: 45rpx;
    }

    .deliver_goods_right {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #777777;
      line-height: 45rpx;
    }
  }
}

/* 发货地址及运费 end */

/* 活动 start */
.activity {
  background: #ffffff;
  // margin-top: 20rpx;
  padding: 16rpx 0;
  box-sizing: border-box;

  .activity_coupons_tips {
    font-size: 28rpx;
    // font-family: PingFang SC;
    font-weight: 500;
    color: #666666;
    line-height: 45rpx;
    white-space: nowrap;
  }

  /* 领券 start */
  .activity_coupons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0 8rpx 0 20rpx;

    .activity_coupons_left {
      display: flex;
      align-items: center;

      .activity_coupons_center {
        display: flex;
        align-items: center;

        .activity_coupons_title {
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: var(--color_main);
          margin: 0 20rpx 0 35rpx;
        }

        .activity_coupons_list {
          display: flex;
          align-items: center;

          .activity_coupons_pre {
            width: 181rpx;
            height: 42rpx;
            background-size: 100% 100%;
            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: var(--color_main);
            line-height: 42rpx;
            text-align: center;
            margin-right: 20rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 0 10rpx;
            position: relative;
            padding-left: 12rpx;
          }

          .activity_coupons_pre_img {
            position: absolute;
            left: 0;
            top: 0;
          }

          .activity_coupons_pre_short {
            padding-left: 10rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .activity_conpons_right {
      width: 36rpx;
      height: 36rpx;

      image {
        width: 36rpx;
        height: 36rpx;
      }
    }
  }

  /* 领券 end */
  /* 满优惠 start */
  .full_discount {
    margin-top: 28rpx;
    padding-right: 20rpx;
    padding-left: 109rpx;
    display: flex;
    align-items: flex-start;

    &.padd20 {
      padding-left: 20rpx !important;
      margin-top: 0rpx !important;
    }

    .full_discount_title {
      white-space: nowrap;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: var(--color_main);
    }

    .discount_title_no_ma {
      line-height: 46rpx;
      margin-left: 20rpx;
    }

    .full_discount_list {
      //wx-4-start
      /* #ifdef MP-WEIXIN */
      /* display: -webkit-box; */
      display: flex;
      flex-wrap: wrap;
      margin-left: 8rpx;
      overflow: hidden;
      /* #endif */
      //wx-4-end
      /* #ifndef MP-WEIXIN */
      display: flex;
      align-items: flex-start;
      margin-left: 19rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #333333;
      white-space: nowrap;
      word-break: break-all;
      text-overflow: ellipsis;
      overflow: hidden;
      word-wrap: break-word;
      /* #endif */
    }
  }

  /* 满优惠 end */
}

/* 活动 end */

/* 积分 start */
.integral {
  background: #ffffff;

  .integral_content {
    border-top: 1rpx solid #f2f2f2;
    display: flex;
    align-items: center;
    height: 100rpx;
    padding: 0 20rpx;

    .integral_title {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #666666;
    }

    .integral_con {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #333333;
      margin-left: 35rpx;
    }
  }
}

/* 积分 end */

/* 服务 start */
.service {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8rpx 0 20rpx;
  box-sizing: border-box;
  background: #ffffff;
  margin-top: 20rpx;

  .service_left {
    display: flex;
    align-items: center;
    height: 90rpx;

    .service_title {
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #666666;
      line-height: 45rpx;
      margin-right: 37rpx;
    }

    .service_con {
      display: flex;
      align-items: center;
      display: inline;
      width: 580rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;

      .service_pre {
        display: inline-block;
        margin-right: 20rpx;

        .service_pre_tips {
          width: 5rpx;
          height: 5rpx;
          background: var(--color_price);
          border-radius: 50%;
          margin-right: 10rpx;
          display: inline-block;
          vertical-align: middle;
        }

        text:nth-child(2) {
          font-size: 24rpx;
          font-family: PingFang SC;
          font-weight: 400;
          color: #555555;
          line-height: 90rpx;
        }
      }
    }
  }

  .service_right {
    display: flex;
    align-items: center;

    image {
      width: 36rpx;
      height: 36rpx;
    }
  }
}

/* 服务 end */

/* 服务弹框 start */
.service_model {
  width: 100%;
  height: 640rpx;
  background: #ffffff;
  border-radius: 15rpx 15rpx 0 0;

  .service_model_top {
    width: 100%;
    height: 100rpx;
    border-radius: 15rpx 15rpx 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12rpx 0 30rpx;
    box-sizing: border-box;
    z-index: 10;
    border-bottom: 1rpx solid #f2f2f2;

    text {
      font-size: 32rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #333333;
      line-height: 32rpx;
    }

    image {
      width: 46rpx;
      height: 46rpx;
    }
  }

  .service_model_list {
    box-sizing: border-box;
    height: 540rpx;

    .service_list_pre {
      margin-left: 30rpx;
      border-bottom: 1rpx solid #f5f5f5;
      padding: 30rpx 30rpx 30rpx 0;
      box-sizing: border-box;

      .service_list_title {
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 45rpx;
      }

      .service_list_des {
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 45rpx;
        margin-top: 19rpx;
        word-break: break-all;
      }
    }
  }
}

/* 服务弹框 end */

/* 优惠券弹框 start */
.coupon_model {
  width: 100%;
  height: 900rpx;
  background: #f5f5f5;
  border-radius: 15rpx 15rpx 0 0;

  .coupon_model_title {
    width: 100%;
    height: 100rpx;
    border-radius: 15rpx 15rpx 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12rpx 0 30rpx;
    box-sizing: border-box;
    position: absolute;
    z-index: 10;
    top: 0;
    background: #ffffff;
    border-bottom: 1rpx solid #f2f2f2;

    text {
      font-size: 32rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #333333;
      line-height: 32rpx;
    }

    image {
      width: 46rpx;
      height: 46rpx;
    }
  }

  .coupon_model_list {
    box-sizing: border-box;
    height: 880rpx;
    width: 750rpx;
    overflow-x: hidden;
    padding: 120rpx 20rpx 0;
    box-sizing: border-box;

    .my_coupon_pre {
      margin-bottom: 20rpx;
      position: relative;

      .coupon_pre_top {
        width: 710rpx;
        height: 190rpx;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        position: relative;

        .coupon_pre_top_bg_img {
          position: absolute;
          left: 0;
          top: 0;
          width: 591rpx;
          height: 190rpx;
        }

        .to_youhuiquan {
          position: absolute;
          right: -26rpx;
          top: 0;
        }

        .coupon_pre_left {
          position: relative;
          display: flex;
          flex-direction: column;
          width: 203rpx;
          align-items: center;

          .coupon_pre_price {
            position: relative;
            font-size: 20rpx;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: var(--color_coupon_main);
            line-height: 31rpx;
            display: flex;
            align-items: baseline;

            text:nth-child(2) {
              position: relative;
              font-size: 48rpx;
              font-family: Source Han Sans CN;
              font-weight: bold;
              color: var(--color_coupon_main);
              line-height: 31rpx;
            }

            .price_int {
              position: relative;
              text-align: center;
              word-break: break-all;
            }
          }

          .coupon_pre_price_high {
            position: relative;
            left: 2rpx;
            top: 14rpx;
            margin-top: 8rpx;

            text:nth-child(2) {
              line-height: 40rpx;
            }
          }

          .coupon_pre_active {
            font-size: 24rpx;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: var(--color_coupon_main);
            line-height: 31rpx;
            text-align: center;
            margin-top: 20rpx;
          }
        }

        .coupon_pre_cen {
          position: relative;
          display: felx;
          flex-direction: column;
          flex: 1;
          padding-left: 44rpx;

          .coupon_pre_title {
            font-size: 30rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: #111111;
            line-height: 31rpx;
          }

          .coupon_pre_time {
            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 31rpx;
            margin: 21rpx 0 17rpx;
          }

          .coupon_pre_rules {
            display: flex;
            align-items: center;

            text {
              font-size: 24rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #999999;
              line-height: 31rpx;
            }

            image {
              width: 12rpx;
              height: 7rpx;
              margin-left: 20rpx;
            }
          }
        }

        .coupon_pre_right {
          position: relative;
          width: 130rpx;
          box-sizing: border-box;
          font-size: 24rpx;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          text-align: center;
        }
      }

      .coupon_rules {
        position: relative;
        width: 710rpx;
        padding: 20rpx 43rpx;
        box-sizing: border-box;
        font-size: 22rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #666666;
        line-height: 30rpx;
        background: #ffffff;
        border-top: 1rpx solid #f2f2f2;
        border-radius: 0 0 15rpx 15rpx;

        .coupon_rules_title {
          margin-bottom: 10rpx;
        }
      }

      .coupon_type {
        position: absolute;
        top: 0;
        left: 0;
        padding: 0 5rpx;
        height: 30rpx;
        background: var(--color_coupon_main);
        font-size: 20rpx;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        line-height: 30rpx;
        text-align: center;
        border-radius: 15rpx 0 15rpx 0;
      }

      .coupon_progress {
        position: absolute;
        width: 130rpx;
        top: 10rpx;
        right: 0rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 18rpx;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        line-height: 31rpx;

        .progress_con {
          position: relative;
          width: 84rpx;
          margin-top: 5rpx;
          border-radius: 5rpx;

          progress {
            border: 1rpx solid #ffffff;
            border-radius: 5rpx;
          }
        }
      }
    }
  }
}

/* 优惠券弹框 end */

/* 满优惠弹框 */
.fulldis_model {
  width: 100%;
  height: 900rpx;
  background: #ffffff;
  border-radius: 15rpx 15rpx 0 0;

  .fulldis_model_title {
    width: 100%;
    height: 100rpx;
    border-radius: 15rpx 15rpx 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12rpx 0 30rpx;
    box-sizing: border-box;
    position: absolute;
    z-index: 10;
    top: 0;
    background: #ffffff;
    border-bottom: 1rpx solid #f2f2f2;

    text {
      font-size: 32rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #333333;
      line-height: 32rpx;
    }

    image {
      width: 46rpx;
      height: 46rpx;
    }
  }

  .fulldis_model_list {
    padding-top: 150rpx;
    box-sizing: border-box;
    width: 750rpx;
    height: 720rpx;
    /* background: #FFFFFF; */
    border-radius: 15rpx 15rpx 0 0;

    .fulldis_model_pre {
      display: flex;
      padding-left: 44rpx;
      box-sizing: border-box;
      margin-bottom: 57rpx;
      flex-shrink: 0;

      .fulldis_pre_tips {
        width: 10rpx;
        height: 10rpx;
        background: var(--color_main);
        border-radius: 50%;
        margin-top: 15rpx;
      }

      .fulldis_pre_con {
        width: 650rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: bold;
        color: #333333;
        line-height: 39rpx;
        margin-left: 20rpx;
      }
    }
  }

  .full_dis_tips {
    font-size: 22rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #999999;
    line-height: 38rpx;
    padding: 20rpx 69rpx 60rpx 66rpx;
    box-sizing: border-box;
    background: #ffffff;
  }
}

/* 评价 start*/
.eva_section {
  display: flex;
  flex-direction: column;
  padding: 30rpx 0 30rpx;
  background: #fff;
  margin-top: 20rpx;

  .e_header {
    height: 33rpx;
    display: flex;
    align-items: center;
    padding: 0 8rpx 0 20rpx;
    box-sizing: border-box;

    .left {
      color: #333333;

      /* 				display: flex;
				align-items: center; */
      .tit {
        font-size: 30rpx;
      }

      .e_num {
        font-size: 30rpx;
        margin-left: 6rpx;
        padding-bottom: 4rpx;
      }

      .e_rate {
        color: #666;
        font-size: 22rpx;
        margin-left: 19rpx;
      }
    }

    .right {
      color: #666666;

      .view_more {
        font-size: 24rpx;
      }

      .iconfont {
        font-size: 18rpx;
      }
    }
  }

  .eva_box {
    .e_member_info {
      margin-top: 30rpx;
      padding: 0 20rpx;

      .portrait {
        width: 50rpx;
        height: 50rpx;
        border-radius: 50%;
      }

      .name {
        color: #2d2d2d;
        font-size: 26rpx;
        margin: 0 20rpx;
      }
    }

    .con {
      color: #333333;
      font-size: 26rpx;
      line-height: 38rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-word;
      width: 710rpx;
      margin: 20rpx auto 0;
    }

    .view_more_eva {
      width: 100%;
      color: #2d2d2d;
      font-size: 26rpx;
      margin-top: 22rpx;

      &:before {
        content: " ";
        width: 160rpx;
        height: 1rpx;
        background: rgba(0, 0, 0, 0.1);
        margin-right: 20rpx;
      }

      &:after {
        content: " ";
        width: 160rpx;
        height: 1rpx;
        background: rgba(0, 0, 0, 0.1);
        margin-left: 20rpx;
      }
    }
  }
}

/* 价 end */

/* 店铺 start */
.shop {
  background-color: #ffffff;
  // margin-top: 20rpx;
  padding-top: 15rpx;
  padding-bottom: 15rpx;

  .shop_des {
    display: flex;
    align-items: center;

    .shop_des_image {
      width: 50rpx;
      height: 50rpx;
      border-radius: 15rpx;
      margin: 0 20rpx;

      image {
        width: 50rpx;
        height: 50rpx;
        border-radius: 15rpx;
      }
    }

    .shop_des_con {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .shop_con_title {
        position: relative;
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #2d2d2d;
        line-height: 45rpx;
      }

      .shop_con_type {
        display: flex;
        align-items: center;

        .shop_type {
          padding: 0 20rpx;
          height: 30px;
          color: #ffffff;
          background: var(--color_main);
          border-radius: 15px;
          font-size: 26px;
          text-align: center;
          line-height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          transform: scale(0.5);
          margin-left: -15px;
          margin-right: 5px;
        }

        .shop_follow_num {
          font-size: 24rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #999999;
          line-height: 45rpx;
        }
      }
    }
  }

  .shop_des_list {
    display: flex;
    align-items: center;
    padding: 0 20rpx 30rpx 20rpx;
    margin-top: 30rpx;

    .shop_des_pre {
      display: flex;
      align-items: center;
      border-right: 1rpx solid #f2f2f2;
      padding-right: 25rpx;
      margin-right: 25rpx;
      white-space: nowrap;

      &:nth-last-child(1) {
        padding-right: 0;
        margin-right: 0;
        border-right: 0;
      }

      text:nth-of-type(1) {
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #555555;
        line-height: 45rpx;
        white-space: nowrap;
      }

      text:nth-of-type(2) {
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: bold;
        color: var(--color_main);
        line-height: 45rpx;
        margin: 0 8rpx;
        white-space: nowrap;
      }

      image {
        width: 30rpx;
        height: 30rpx;
      }

      .shop_des_pre_img {
        width: 30rpx;
        height: 30rpx;
        font-size: 20rpx;
        background: var(--color_halo);
        display: flex;
        align-items: center;
        color: var(--color_main);
        border-top-left-radius: 50%;
        border-top-right-radius: 50%;
        border-bottom-right-radius: 50%;
        justify-content: center;
      }
    }
  }

  .shop_links {
    height: 108rpx;
    border-top: 1rpx solid #f2f2f2;
    display: flex;
    justify-content: space-between;
    padding: 0 149rpx 0 161rpx;
    box-sizing: border-box;
    align-items: center;

    image {
      width: 172rpx;
      height: 48rpx;
    }
  }
}

/* 店铺 end */

/* 店铺推荐 start */
.store_recommend {
  width: 750rpx;
  background: #fff;
  margin-top: 20rpx;
  padding: 30rpx 0;
}

.store_recommend .store_recommend_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}

.store_recommend .store_recommend_top .store_recommend_title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.store_recommend .store_recommend_list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.store_recommend .store_recommend_list .store_recommend_pre {
  width: 33.33%;
  padding: 0 10rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.store_recommend .store_recommend_list .store_recommend_pre .store_reco_pre_image {
  width: 100%;
  position: relative;

  .image {
    width: 100%;
    height: 220rpx;
    background-size: cover;
    background-position: center;
    border-radius: 12rpx;
  }
}

.store_recommend .store_recommend_list .store_recommend_pre .store_reco_pre_name {
  font-size: 26rpx;
  color: #333;
  margin-top: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.3;
  min-height: 40rpx;
}

.store_recommend .store_recommend_list .store_recommend_pre .store_reco_pre_price {
  font-size: 28rpx;
  color: #FF2C3C;
  font-weight: bold;
  margin-top: 4rpx;

  &::before {
    content: '¥';
    font-size: 24rpx;
  }
}

/* 店铺推荐 end */

/* 规格参数 start */
.spec_param {
  background: #ffffff;
  margin-top: 20rpx;
  padding-bottom: 30rpx;

  .spec_param_title {
    display: flex;
    flex-direction: column;
    padding: 30rpx 0 0 38rpx;

    text {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #2d2d2d;
      line-height: 36rpx;
    }

    .image {
      width: 660rpx;
      height: 22rpx;
      background-position: center center;
      background-repeat: no-repeat;
      background-size: cover;
    }
  }

  .spec_param_list {
    padding: 0 20rpx;
    max-height: 350rpx;
    margin-top: 20rpx;
    box-sizing: border-box;
    overflow: hidden;

    .spec_param_pre {
      width: 710rpx;
      display: flex;
      align-items: center;
      font-size: 24rpx;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #777777;
      line-height: 36rpx;
      border: 1rpx solid #f2f2f2;
      border-bottom: 0;
      padding: 8rpx 0;

      view:nth-child(1) {
        display: flex;
        align-items: center;
        width: 200rpx;
        display: inline-block;
        justify-content: flex-end;
        /* line-height: 70rpx; */
        text-align: right;
        padding: 0 20rpx;
        height: 100%;
      }

      view:nth-child(2) {
        width: 541rpx;
        /* height: 70rpx; */
        line-height: 72rpx;
        padding-left: 20rpx;
        box-sizing: border-box;
        border-left: 1rpx solid #f2f2f2;
      }
    }

    .spec_param_pre:nth-last-child(1) {
      border-bottom: 1rpx solid #f2f2f2;
    }
  }

  .open_param {
    height: auto;
    max-height: unset;
  }

  .spec_param_fold {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 30rpx;

    text {
      font-size: 24rpx;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #777777;
    }

    image {
      width: 20rpx;
      height: 12rpx;
      margin-left: 10rpx;
    }
  }
}

/* 规格参数 end */

/*  详情 */
.detail-desc {
  background: #fff;
  // margin-top: 20rpx;
  overflow-x: hidden;
  padding: 30rpx 20rpx;
  box-sizing: border-box;

  .detail-desc_title {
    margin-bottom: 30rpx;
    display: flex;
    flex-direction: column;

    text {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #2d2d2d;
      line-height: 36rpx;
      text-align: center;
    }

    .image {
      width: 660rpx;
      height: 22rpx;
      background-position: center center;
      background-repeat: no-repeat;
      background-size: contain;
    }
  }
}

/*  弹出层 */
.popup {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 99;

  &.show {
    display: block;

    .mask {
      animation: showPopup 0.2s linear both;
    }

    .layer {
      animation: showLayer 0.2s linear both;
    }
  }

  &.hide {
    .mask {
      animation: hidePopup 0.2s linear both;
    }

    .layer {
      animation: hideLayer 0.2s linear both;
    }
  }

  &.none {
    display: none;
  }

  .mask {
    position: fixed;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.4);
  }

  .layer {
    position: fixed;
    z-index: 99;
    bottom: 0;
    width: 100%;
    min-height: 40vh;
    border-radius: 10rpx 10rpx 0 0;
    background-color: #fff;

    .btn {
      height: 66rpx;
      line-height: 66rpx;
      border-radius: 100rpx;
      background: $uni-color-primary;
      font-size: $font-base;
      color: #fff;
      margin: 30rpx auto 20rpx;
    }
  }

  .back_view {
    display: flex;
    align-items: center;

    view {
      width: 30rpx;
      height: 30rpx;
    }

    text {
      font-size: 28rpx;
    }
  }

  @keyframes showPopup {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes hidePopup {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @keyframes showLayer {
    0% {
      transform: translateY(120%);
    }

    100% {
      transform: translateY(0%);
    }
  }

  @keyframes hideLayer {
    0% {
      transform: translateY(0);
    }

    100% {
      transform: translateY(120%);
    }
  }
}

.uni-swiper-dot,
.swiper-box,
.video_btn,
.swiper_item,
.slide-image {
  width: 100%;
  height: 750rpx;
}

.videoImage {
  width: 100%;
  height: 750rpx;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.bottom_block {
  width: 750rpx;
  height: calc(100rpx + constant(safe-area-inset-bottom));
  /* 兼容 iOS < 11.2 */
  height: calc(100rpx + env(safe-area-inset-bottom));
  /* 兼容 iOS >= 11.2 */
}

/* 底部操作菜单 */
.page_bottom {
  position: fixed;
  left: 0rpx;
  right: 0rpx;
  margin: 0 auto;
  bottom: 0rpx;
  z-index: 95;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  width: 750rpx;
  /*height: 98rpx;*/
  background: #fff;
  box-shadow: 0px 0px 20px 0px rgba(86, 86, 86, 0.2);
  padding-bottom: constant(safe-area-inset-bottom);
  /* 兼容 iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom);
  /* 兼容 iOS >= 11.2 */
  box-sizing: border-box;

  .p_b_btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: $font-sm;
    color: $font-color-base;
    width: 96rpx;
    height: 80rpx;
    position: relative;
    margin-top: 6rpx;
    margin-bottom: 6rpx;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 2rpx;
    }

    .show_text {
      color: #2d2d2d;
      font-size: 20rpx;
    }

    .cart_num {
      position: absolute;
      width: 30rpx;
      height: 30rpx;
      text-align: center;
      background: var(--color_main);
      border-radius: 50%;
      font-size: 18rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      line-height: 30rpx;
      right: 10rpx;
      top: -8rpx;
      z-index: 5;
    }
  }

  .ladder_btn {
    width: 420rpx;
    height: 70rpx;
    background: var(--color_ladder_main_bg);
    border-radius: 35rpx;
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
    line-height: 70rpx;
    text-align: center;
  }

  .action_btn_group {
    display: flex;
    height: 70rpx;
    overflow: hidden;
    margin-left: 20rpx;
    margin-right: 20rpx;

    .action_btn {
      height: 100%;
      font-size: 32rpx;
      color: #fff;

      &::after {
        border: none;
      }
    }

    .add_cart_btn {
      width: 223rpx;
      background: var(--color_vice_bg);
      border-radius: 35rpx 0 0 35rpx;
    }

    .buy_now_btn {
      width: 197rpx;
      background: var(--color_main);
      border-radius: 0 35rpx 35rpx 0;
    }

    .virtual_buy {
      width: 420rpx;
      background: var(--color_main);
      border-radius: 35rpx;
    }

    .not_stock {
      width: 420rpx;
      background: #adadad;
      border-radius: 35rpx;
    }

    .instant_second_kill {
      width: 420rpx;
      height: 70rpx;
      background: var(--color_seckill_main_bg);
      border-radius: 35rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      line-height: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .instant_pay_deposit {
      width: 420rpx;
      height: 70rpx;
      background: linear-gradient(45deg, #ff7a18 0%, #fea10e 100%);
      border-radius: 35rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 26rpx;
    }

    .seckill_finished {
      width: 420rpx;
      height: 70rpx;
      background: #999999;
      border-radius: 35rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 30rpx;
    }

    .preSale_btn_deposit {
      width: 420rpx;
      height: 70rpx;
      background: var(--color_presell_vice);
      border-radius: 35rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      line-height: 30rpx;
      padding: 0 20rpx;
    }

    .preSale_btn_buy {
      height: 70rpx;
      background: linear-gradient(45deg,
          rgba(252, 45, 45, 1) 0%,
          rgba(253, 87, 43, 1) 100%);
      border-radius: 0 35rpx 35rpx 0;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      line-height: 30rpx;
      padding: 0 30rpx;
    }

    .action_btn_seck {
      background: var(--color_seckill_vice);
    }
  }
}

button {
  padding: 0;
  margin: 0;
}

.buy_now_btn_seck {
  background: var(--color_seckill_main) !important;
}

.spec_model_con {
  width: 750rpx;
  height: 900rpx;
  background: #ffffff;
  border-radius: 10rpx 10rpx 0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  z-index: 150;

  .spec_model_content {
    padding-bottom: 115rpx;

    .spec_model_top {
      display: flex;
      justify-content: space-between;
      padding: 30rpx 22rpx 0 30rpx;
      box-sizing: border-box;

      .spec_model_goods {
        display: flex;
        height: 151rpx;
        /* align-items: center; */

        .spec_goods_image {
          width: 151rpx;
          height: 151rpx;
          background: #eeeeee;
          border-radius: 15rpx;

          image {
            width: 151rpx;
            height: 151rpx;
            border-radius: 15rpx;
          }
        }

        .spec_goods_right {
          margin-left: 30rpx;
          flex-shrink: 0;

          .spec_goods_price_con {
            display: flex;
            align-items: flex-start;

            .spec_prices {
              
              .spec_goods_price {
                margin-right: 18rpx;
                
                .original_price {
                  margin-top: -8rpx;
                  display: block;
                }

                text {
                  font-size: 24rpx;
                  font-family: PingFang SC;
                  font-weight: 500;
                  color: var(--color_price);
                }

                text:nth-child(2) {
                  font-size: 50rpx;
                }
              }
            }

            .sec_kill_tips {
              width: 130rpx;
              height: 40rpx;
              background: var(--color_seckill_main_bg);
              border-radius: 20rpx;
              font-size: 24rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #ffffff;
              text-align: center;
              line-height: 40rpx;
              margin-left: 20px;
            }

            .pre_sale_tips {
              width: 76rpx;
              height: 38rpx;
              background: var(--color_presell_main_bg);
              border-radius: 18rpx;
              font-size: 22rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 20px;
            }

            .ladder_regiment_tips {
              width: 100rpx;
              height: 40rpx;
              background: var(--color_ladder_main_bg);
              border-radius: 20rpx;
              font-size: 24rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #ffffff;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 20rpx;
            }

            .pin_tips {
              width: 80rpx;
              height: 40rpx;
              background: var(--color_spell_main_bg);
              border-radius: 20rpx;
              font-size: 24rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #ffffff;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 20rpx;
            }
          }

          .spec_goods_des {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #343434;
            margin-top: 15rpx;
            width: 520rpx;
            /* white-space: nowrap;
							text-overflow: ellipsis;
							overflow: hidden; */
            word-break: break-all;
          }
        }
      }

      .close_spec {
        position: absolute;
        top: 14rpx;
        right: 14rpx;
        z-index: 9;
        width: 46rpx;
        height: 46rpx;
      }
    }

    .spec_content {
      height: 620rpx;

      .spec_list {
        margin: 0 30rpx;
        padding-top: 34rpx;

        .spec_list_pre {
          border-bottom: 1rpx solid #f5f5f5;

          .spec_list_pre_name {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #666666;
            margin-bottom: 30rpx;
          }

          .spec_list_pre_desc {
            display: inline-table;
            padding: 13rpx 25rpx;
            box-sizing: border-box;
            box-sizing: border-box;
            background: #f5f5f5;
            border-radius: 50rpx;
            margin-bottom: 30rpx;
            margin-right: 30rpx;
            border: 1rpx solid #f5f5f5;

            .spec_list_pre_con {
              display: flex;
              align-items: center;

              text {
                font-size: 26rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #343434;
                text-align: left;
              }

              image {
                width: 36rpx;
                height: 36rpx;
                margin-right: 20rpx;
              }
            }
          }

          .spec_list_pre_desc_active {
            background: #ffffff;
            border: 1rpx solid var(--color_main);

            .spec_list_pre_con {
              text {
                color: var(--color_main);
              }
            }
          }

          .spec_list_pre_desc_disabled {
            background: #f5f5f5;
            opacity: 0.2;

            .spec_list_pre_con {
              text {
                color: #2d2d2d;
              }
            }
          }
        }
      }

      .spec_num {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 0 20rpx 0 30rpx;
        box-sizing: border-box;
        margin-top: 16rpx;

        .spec_num_left {
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #666666;

          text {
            color: #949494;
          }
        }

        .spec_num_right {
          height: 60rpx;
          border: 1rpx solid #e5e5e5;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 24rpx;
          font-family: PingFang SC;
          font-weight: bold;
          color: #a6a6a6;
          line-height: 30rpx;
          border-radius: 30rpx;
          overflow: hidden;

          text {
            width: 60rpx;
            height: 60rpx;
            text-align: center;
            line-height: 60rpx;
            border-left: 1px solid #e5e5e5;
            font-size: 34rpx;
            color: #333;

            &.no_edit {
              background: #ededed;
              opacity: 0.5;
              color: #949494;
            }
          }

          text:nth-child(1) {
            color: #949494;
            border-right: 1rpx solid #e5e5e5;
            border-left: none;
          }

          .spec_num_input_wrap {
            padding: 4rpx 8rpx;
          }

          input {
            width: 88rpx;
            height: 48rpx;
            line-height: 48rpx;
            text-align: center;
            font-size: 24rpx;

            font-size: 28rpx;
            color: #2d2d2d;
          }
        }

        .buyLimit {
          color: #e2231a;
          font-size: 24rpx;
        }
      }
    }
  }

  .spec_btn {
    width: 750rpx;
    height: 98rpx;
    background: #ffffff;
    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(86, 86, 86, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: env(safe-area-inset-bottom);
    //wx-11-start
    /* #ifdef MP */
    /* height: calc(98rpx + env(safe-area-inset-bottom)); */
    /* padding-bottom: constant(safe-area-inset-bottom); */
    /*兼容 IOS<11.2*/
    /* padding-bottom: env(safe-area-inset-bottom); */
    /*兼容 IOS>11.2*/
    /* #endif */
    //wx-11-end
    color: #fff;

    .spec_add_cart_btn {
      width: 345rpx;
      height: 70rpx;
      background: var(--color_vice_bg);
      border-radius: 35rpx 0 0 35rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      line-height: 70rpx;
    }

    .spec_buy_btn {
      width: 345rpx;
      height: 70rpx;
      background: var(--color_main);
      border-radius: 0 35rpx 35rpx 0;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      line-height: 70rpx;
    }

    .spec_buy_btn_seck {
      background: var(--color_seckill_main);
    }

    .spec_not_stock {
      background: #adadad;
      border-radius: 35rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      line-height: 70rpx;
    }

    .spec_seckill_btn {
      background: linear-gradient(45deg, #fc2d2d 0%, #fd572b 100%);
      border-radius: 35rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      line-height: 70rpx;
    }

    .spec_seckill_btn_seck {
      background: var(--color_seckill_main_bg);
    }

    .spec_btn_only {
      width: 690rpx;
      height: 70rpx;
      border-radius: 35rpx;
      text-align: center;
      line-height: 70rpx;
    }

    .specifications_btn2 {
      width: 690rpx;
      height: 70rpx;
      background: var(--color_ladder_main_bg);
      border-radius: 35rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 32rpx;
    }

    .specifications_bottom_btn3 {
      width: 690rpx;
      height: 70rpx;
      background: var(--color_spell_vice);
      border-radius: 35rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .specifications_bottom_btn4 {
      width: 690rpx;
      height: 70rpx;
      background: var(--color_spell_main_bg);
      border-radius: 35rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .specification_add {
      width: 347rpx;
      height: 70rpx;
      background: var(--color_spell_vice);
      border-radius: 34rpx 0 0 34rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 28rpx;
    }

    .specification_add text:nth-of-type(1),
    .specification_buy text:nth-of-type(1) {
      margin-right: 20rpx;
    }

    .spec_deposit_btn {
      color: #fff;
      background: var(--color_presell_vice);
    }

    .specification_buy {
      width: 343rpx;
      height: 70rpx;
      background: var(--color_spell_main);
      border-radius: 0 34rpx 34rpx 0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 28rpx;
    }
  }
}

/* //sdasdasd */
.right_down {
  width: 36rpx;
  height: 36rpx;
}

.play_btn {
  width: 90rpx;
  height: 90rpx;
  position: absolute;
  left: 50%; //起始是在body中，横向距左50%的位置
  top: 50%; //起始是在body中，纵向距上50%的位置，这个点相当于body的中心点，div的左上角的定位
  transform: translate(-50%, -50%); //水平、垂直都居中,也可以写成下面的方式
}

.uni-transition {
  width: 750rpx;
  margin: 0 auto;
}

.address_list {
  width: 750rpx;
  height: 680rpx;
  margin: 0 auto;
  z-index: 150;
  background-color: #fff;
}

.address_list_con {
  border-radius: 5px 5px 0;
}

.other_address {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 130rpx;
  background: #fff;

  .other_btn {
    width: 668rpx;
    height: 80rpx;
    background: var(--color_main_bg);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 34rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #fefefe;
  }
}

.address_top {
  padding: 20rpx 30rpx;
  border-radius: 5px 5px 0 0;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #333333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  border-bottom: 0.5px solid #f2f2f2;

  image {
    width: 50rpx;
    height: 50rpx;
  }
}

.list {
  display: flex;
  /* flex-direction: column; */
  align-items: center;
  justify-content: flex-start;
  padding: 24rpx 30rpx;
  background: #fff;
  position: relative;

  &.b-b {
    /* &:after {
				position: absolute;
				z-index: 3;
				left: 20rpx;
				right: 0;
				height: 0;
				content: '';
				-webkit-transform: scaleY(0.5);
				transform: scaleY(0.5);
				border-bottom: 1px solid rgba(0, 0, 0, .1);
			} */
  }
}

.wrapper {
  flex: 1;
  background: #fff;

  .iconfont {
    color: var(--color_main);
    font-size: 32rpx;
    margin-right: 30rpx;
  }

  image {
    width: 36rpx;
    height: 38rpx;
    margin-right: 22rpx;
  }

  .svgGroup {
    margin-right: 22rpx;
  }
}

.wrapper_right {
  .checkedIcon {
    width: 32rpx;
    height: 24rpx;
    margin-left: 30rpx;
  }
}

.address-box {
  display: flex;
  align-items: center;

  .address {
    font-size: 28rpx;
    color: #333;
    line-height: 38rpx;
    margin-top: 5rpx;
    word-break: break-all;
    max-width: 570rpx;
  }

  .tag {
    width: 63rpx;
    height: 30rpx;
    margin-left: 20rpx;
    margin-right: 0rpx;
  }
}

.address_on {
  // color: #fb4444 !important;
}

.u-box {
  font-size: 30rpx;
  color: $font-color-light;
  color: $main-font-color;
  font-weight: bold;

  .name {
    margin-right: 70rpx;
  }
}

.fulldis_pre_con ::v-deep #top>div div {
  overflow: unset;
  white-space: normal;
  text-overflow: unset;
}

/* 随机优惠券 start */
.random_coupon.hide {
  display: none;
}

.random_coupon {
  display: flex;
  justify-content: center;
  padding-top: 250rpx;
  position: fixed;
  top: 0;
  width: 750rpx;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;

  .random_coupon_bg {
    width: 598rpx;
    height: 804rpx;
    background-size: 100% 100%;
    padding-top: 330rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .random_coupon_price {
      font-size: 68rpx;
      font-family: PangMenZhengDao;
      font-weight: 400;
      color: #d41e04;
    }

    .random_coupon_des {
      font-size: 30rpx;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #e52308;
      line-height: 34rpx;
    }

    .close_btn {
      position: absolute;
      right: 10rpx;
      top: 22rpx;
      z-index: 20;
      width: 57rpx;
      height: 57rpx;
      background-size: 100% 100%;
    }
  }
}

/* 随机优惠券 end */

.search_wrap {
  z-index: 99;
  position: fixed;
  top: 0;
  /* #ifdef H5 */
  padding-top: 28rpx !important;
  /* #endif */
  /* 原是 MP || APP-PLUS*/
  //app-8-start

  //app-8-end
  left: 0;
  right: 0;

  .searchs {
    width: 710rpx;
    height: 60rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    /* #ifndef MP */
    margin: 20rpx 20rpx 0 20rpx;
    background: transpanrent;
    margin: 0 auto;
    /* #endif */
    z-index: 99;
  }

  .search_mp {
    padding: 0 25rpx;
  }

  .search_mp .search_mp_name {
    // width: 95rpx;

    font-size: 34rpx;
    font-family: PingFang SC;
    font-weight: bold;
    color: #ffffff;
    margin-right: 20rpx;
    text-align: center;
    display: flex;
    align-items: center;
    border: 1rpx solid #efeaea;
    background: rgba(255, 255, 255, 0.85);
    position: relative;

    image {
      width: 19rpx;
      height: 33rpx;
    }

    .more_tips_xian {
      width: 1rpx;
      height: 32rpx;
      background: #000000;
      opacity: 0.1;
      position: absolute;
      left: 46%;
      transform: translate(-50%);
    }
  }

  .searchs .left {
    // width: 400rpx;
    height: 60rpx;
    border-radius: 30rpx;
    // background: rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;

    image {
      width: 48rpx;
      height: 48rpx;
    }
  }

  .search_mp .left {
    // width: auto;
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .favicon {
      background: rgba(255, 255, 255, 0.4);
      width: 52rpx;
      height: 52rpx;
      border-radius: 50%;

      .iconfont {
        font-size: 38rpx;
        color: #333;
      }

      .iconyishoucang {
        color: var(--color_main);
      }
    }
  }

  .searchs .left image {
    width: 50rpx;
    height: 50rpx;
    margin-top: 2rpx;
    margin-left: 10rpx;
  }

  .search_mp .left image {
    width: 54rpx;
    height: 54rpx;
    margin-top: 7rpx;
  }

  .searchs .left text {
    color: #fff;
    font-size: 24rpx;
    margin-top: -2rpx;
  }

  .searchs .search_text {
    font-size: 26rpx;
    color: #fff;
  }

  /* 三点多分享 */
  .more_tips {
    position: relative;
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 26rpx;
  }

  .more_tips_left {
    width: 50%;
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: flex-start;

    padding-left: 26rpx;
    // padding-right: 31rpx;
  }

  .more {
    width: 34rpx !important;
    height: 27rpx !important;
    // margin-left: 30rpx;
  }

  .triangle-up {
    position: absolute;
    right: 29rpx;
    width: 0;
    height: 0;
    top: 49rpx;
    border-left: 15rpx solid transparent;
    border-right: 15rpx solid transparent;
    border-bottom: 20rpx solid #fcfcfc;
    /* transform: rotate(120deg); */
    transform: rotate(0deg);
    box-shadow: -2rpx 2rpx -1rpx 0rpx rgba(102, 102, 102, 0.1);
    z-index: 21;
  }

  .tips {
    position: absolute;
    z-index: 20;
    top: 65rpx;
    // right: -15rpx;
    left: 50%;
    margin-left: -62rpx;
    width: 226rpx;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 0px 10rpx 0px rgba(102, 102, 102, 0.2);
    opacity: 0.94;
    border-radius: 15rpx;
    display: flex;
    flex-direction: column;
  }

  .tips_pre {
    width: 100%;
    height: 88rpx;
    display: flex;
    align-items: center;
    border-bottom: #e6e6e6;
    padding-left: 20rpx;
    box-sizing: border-box;
  }

  button::after {
    border: none;
  }

  button[plain] {
    border: none;
  }

  .tips_pre image {
    width: 32rpx !important;
    height: 32rpx !important;
    margin-right: 20rpx !important;
    // margin-right: 8rpx;
  }

  .tips_pre text {
    font-size: 26rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
    line-height: 32rpx;
  }
}

.recommendGoods_wrap {
  padding-bottom: calc(env(safe-area-inset-bottom) + 98rpx);
}

.pinInfo_box {
  background-color: var(--color_spell_main);
}

.address_pin {
  color: var(--color_spell_main) !important;
}

.address_pre {
  color: var(--color_presell_main) !important;
}

.address_lad {
  color: var(--color_ladder_main) !important;
}

.address_seck {
  color: var(--color_seckill_main) !important;
}

.other_btn_pin {
  background: var(--color_spell_main) !important;
}

.other_btn_pre {
  background: var(--color_presell_main) !important;
}

.other_btn_lad {
  background: var(--color_ladder_main) !important;
}

.other_btn_seck {
  background: var(--color_seckill_vice) !important;
}

.cart_num_pin {
  color: var(--color_spell_main) !important;
  box-shadow: 0 0 0 1px var(--color_spell_main) inset !important;
}

.cart_num_pre {
  color: var(--color_presell_main) !important;
  box-shadow: 0 0 0 1px var(--color_presell_main) inset !important;
}

.cart_num_lad {
  color: var(--color_ladder_main) !important;
  box-shadow: 0 0 0 1px var(--color_ladder_main) inset !important;
}

.cart_num_seck {
  color: var(--color_seckill_main) !important;
  box-shadow: 0 0 0 1px var(--color_seckill_main) inset !important;
}

.spec_goods_price_pin {
  text {
    color: var(--color_spell_main) !important;
  }
}

.spec_goods_price_pre {
  text {
    color: var(--color_presell_main) !important;
  }
}

.spec_goods_price_seck {
  text {
    color: var(--color_seckill_main) !important;
  }
}

.spec_goods_price_lad {
  text {
    color: var(--color_ladder_main) !important;
  }
}

.spec_list_pre_desc_active_pin {
  background: var(--color_spell_halo) !important;
  border: 1rpx solid var(--color_spell_main) !important;

  text {
    color: var(--color_spell_main) !important;
  }
}

.spec_list_pre_desc_active_pre {
  background: var(--color_presell_halo) !important;
  border: 1rpx solid var(--color_presell_main) !important;

  text {
    color: var(--color_presell_main) !important;
  }
}

.spec_list_pre_desc_active_lad {
  background: var(--color_ladder_halo) !important;
  border: 1rpx solid var(--color_ladder_main) !important;

  text {
    color: var(--color_ladder_main) !important;
  }
}

.spec_list_pre_desc_active_seck {
  background: var(--color_seckill_halo) !important;
  border: 1rpx solid var(--color_seckill_main) !important;

  text {
    color: var(--color_seckill_main) !important;
  }
}

/* 修改弹窗中原价的样式 */
.spec_goods_price {
  // ... 已有样式 ...

  .original_price {
    color: #949494;
    font-size: 22rpx;
    text-decoration: line-through;
    margin-left: 10rpx;
    display: inline-block;
  }
}

/* 会员价原价样式 */
.left_super {
  .original_price {
    color: #949494;
    font-size: 22rpx;
    text-decoration: line-through;
    margin-left: 10rpx;
    display: inline-block;
  }
}

/* 秒杀价原价样式 */
.spec_goods_price_seck {
  .original_price {
    color: #949494;
    font-size: 22rpx;
    text-decoration: line-through;
    margin-left: 10rpx;
    display: inline-block;
  }
}

/* 添加样式 */
.coupon_success_tip {
  position: fixed;
  top: var(--status-bar-height);
  left: 0;
  right: 0;
  z-index: 100;
  height: 60rpx;
  line-height: 60rpx;
  background: var(--color_main);
  color: #fff;
  font-size: 28rpx;
  text-align: center;
}

.activity_coupons_received {
  font-size: 28rpx;
  color: #999 !important;
  /* 使用灰色 */
  padding: 0 20rpx;
}

/* 移除之前的橙色样式 */
.activity_coupons_title.activity_coupons_received {
  color: #999 !important;
  /* 确保使用灰色 */
}

// 分享价格
.commission_info {
  display: flex;
  flex-direction: column;
  margin-left: 20rpx;
  height: 100%;
  justify-content: center;

  .commission_main {
    display: flex;
    align-items: baseline; // 改用baseline对齐

    .commission_label {
      font-size: 24rpx;
      color: #222;
      margin-right: 8rpx;
    }

    .commission_value {
      font-size: 32rpx;
      font-weight: bold;
      color: #ED662E;
    }
  }

  .commission_detail {
    margin-top: 4rpx;
    font-size: 20rpx;
    color: #999;
  }
}

// 规格弹窗中的分享价格样式
.spec_commission_info {
  margin-left: 15rpx;
  margin-top: 18rpx;
  flex-shrink: 0;
  align-self: center;
  
  .spec_commission_main {
    display: flex;
    align-items: baseline;
    
    .spec_commission_label {
      font-size: 22rpx;
      color: #222;
      margin-right: 8rpx;
    }
    
    .spec_commission_value {
      font-size: 28rpx;
      font-weight: bold;
      color: #ED662E;
    }
  }
  
  .commission_detail {
    margin-top: 4rpx;
    font-size: 20rpx;
    color: #999;
  }
}

.store_recommend_list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.store_recommend_pre {
  width: 33.33%;
  padding: 0 10rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.store_reco_pre_image {
  width: 100%;
  position: relative;

  .image {
    width: 100%;
    height: 220rpx;
    background-size: cover;
    background-position: center;
    border-radius: 12rpx;
  }
}

.store_reco_pre_name {
  font-size: 26rpx;
  color: #333;
  margin-top: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.3;
  min-height: 40rpx;
}

.store_reco_pre_price {
  font-size: 28rpx;
  color: #FF2C3C;
  font-weight: bold;
  margin-top: 4rpx;

  &::before {
    content: '¥';
    font-size: 24rpx;
  }
}

.store_recommend_publish {
  .publish_box {
    background: var(--color_main);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);

    .publish_icon {
      font-size: 80rpx;
      color: #fff;
      line-height: 1;
      margin-bottom: 20rpx;
    }

    .publish_text {
      font-size: 32rpx;
      color: #fff;
    }
  }
}

/* 商品名称 */
.goods_name {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--color_font_dark);
  line-height: 44rpx;
  flex: 1;
  position: relative;
}

.goods_sales {
  font-size: 24rpx;
  color: var(--color_font_light);
  margin-left: 20rpx;
  margin-top: 12rpx;
  white-space: nowrap;
}

.widthIndent {
  text-indent: 2em;
}

/* 版本号样式 */
.version_info {
  text-align: center;
  padding: 20rpx 0 40rpx;
  font-size: 24rpx;
  color: #999;
  background: $bg-color-split;
}
</style>
